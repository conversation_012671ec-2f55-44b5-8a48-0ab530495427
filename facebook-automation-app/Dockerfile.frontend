# Facebook Automation App - Frontend Dockerfile
# Multi-stage build for React frontend

# Stage 1: Build stage
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production=false

# Copy source code
COPY . .

# Set build environment variables
ENV REACT_APP_API_URL=http://localhost:8000
ENV REACT_APP_HYBRID_MODE=enabled
ENV GENERATE_SOURCEMAP=false

# Build the application
RUN npm run build

# Stage 2: Production stage with Nginx
FROM nginx:alpine as production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# Create startup script
RUN cat > /docker-entrypoint.sh << 'EOF'
#!/bin/sh
set -e

echo "Starting Facebook Automation Frontend..."

# Replace environment variables in built files
if [ -n "$REACT_APP_API_URL" ]; then
    echo "Setting API URL to: $REACT_APP_API_URL"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|http://localhost:8000|$REACT_APP_API_URL|g" {} \;
fi

if [ -n "$REACT_APP_HYBRID_MODE" ]; then
    echo "Setting Hybrid Mode to: $REACT_APP_HYBRID_MODE"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|\"enabled\"|\"$REACT_APP_HYBRID_MODE\"|g" {} \;
fi

# Start nginx
echo "Starting Nginx..."
exec nginx -g "daemon off;"
EOF

RUN chmod +x /docker-entrypoint.sh

# Health check script
RUN cat > /healthcheck.sh << 'EOF'
#!/bin/sh
curl -f http://localhost:80 || exit 1
EOF

RUN chmod +x /healthcheck.sh

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /healthcheck.sh

# Start command
ENTRYPOINT ["/docker-entrypoint.sh"]
