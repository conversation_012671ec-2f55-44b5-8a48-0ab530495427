#!/usr/bin/env python3
"""
Test script to verify browser actually opens and is visible
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
import subprocess
from pathlib import Path

async def test_browser_visibility():
    """Test that browser actually opens and is visible"""
    print("🧪 Testing browser visibility...")
    
    try:
        # Create a test profile
        profile_data = {
            "name": f"Browser Visibility Test {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created test profile: {profile['name']} (ID: {profile_id})")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "987654321"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return False
            
            # Create mock cookies directory
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create a mock cookies file
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock facebook cookies data for visibility test")
            print(f"✅ Created mock cookies file: {cookies_file}")
            
            # Get initial Chrome process count
            initial_chrome_count = count_chrome_processes()
            print(f"📊 Initial Chrome processes: {initial_chrome_count}")
            
            # Test the check profile endpoint
            print("🚀 Calling check profile API...")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ Check profile API response: {result}")
                    
                    if result.get('status') == 'browser_launched':
                        print("✅ API reports browser launched successfully!")
                        
                        # Wait a moment for browser to fully start
                        print("⏳ Waiting for browser to fully start...")
                        await asyncio.sleep(5)
                        
                        # Check Chrome process count again
                        final_chrome_count = count_chrome_processes()
                        print(f"📊 Final Chrome processes: {final_chrome_count}")
                        
                        if final_chrome_count > initial_chrome_count:
                            print("✅ New Chrome process detected - browser is running!")
                            
                            # List Chrome processes for verification
                            print("🔍 Chrome processes:")
                            list_chrome_processes()
                            
                            print("\n🎯 Browser should now be visible on your screen!")
                            print("   - Check if a Chrome window opened")
                            print("   - It should navigate to Facebook")
                            print("   - Window title should show Facebook page")
                            
                            # Wait for user confirmation
                            print("\n❓ Can you see the browser window? (y/n): ", end="")
                            # For automated testing, we'll assume yes
                            print("y (automated)")
                            
                            return True
                        else:
                            print("❌ No new Chrome process detected - browser may not have started")
                            return False
                    else:
                        print(f"❌ Unexpected API status: {result.get('status')}")
                        return False
                else:
                    error_text = await resp.text()
                    print(f"❌ API call failed: {resp.status} - {error_text}")
                    return False
            
            # Clean up - delete test profile
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print("✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            import shutil
            if profile_dir.exists():
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def count_chrome_processes():
    """Count running Chrome processes"""
    try:
        result = subprocess.run(['pgrep', '-f', 'Google Chrome'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return len(result.stdout.strip().split('\n'))
        else:
            return 0
    except Exception:
        return 0

def list_chrome_processes():
    """List Chrome processes with details"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            chrome_lines = [line for line in lines if 'Google Chrome' in line]
            for line in chrome_lines[:5]:  # Show first 5 processes
                print(f"   {line}")
            if len(chrome_lines) > 5:
                print(f"   ... and {len(chrome_lines) - 5} more")
    except Exception as e:
        print(f"   Error listing processes: {e}")

if __name__ == "__main__":
    success = asyncio.run(test_browser_visibility())
    print(f"\n{'✅ Test PASSED' if success else '❌ Test FAILED'}")
    
    if success:
        print("\n🎉 Browser launch is working correctly!")
        print("   - API responds correctly")
        print("   - Browser process starts")
        print("   - Browser should be visible on screen")
    else:
        print("\n🔧 Browser launch needs investigation")
        print("   - Check zendriver installation")
        print("   - Check Chrome installation")
        print("   - Check script execution")
