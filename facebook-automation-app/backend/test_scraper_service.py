"""
Test script to debug FacebookScraperService initialization
"""
import asyncio
import sys
import os
from loguru import logger

# Add paths
sys.path.insert(0, os.path.dirname(__file__))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'automation'))

async def test_scraper_service():
    """Test FacebookScraperService initialization"""
    logger.info("🧪 Testing FacebookScraperService initialization...")
    
    try:
        # Test 1: Import the module
        logger.info("📦 Testing import...")
        from automation.facebook_scraper_service import create_facebook_scraper_service
        logger.info("✅ Import successful")
        
        # Test 2: Create service
        logger.info("🔧 Creating service...")
        service = await create_facebook_scraper_service("test_data")
        logger.info("✅ Service created successfully")
        logger.info(f"📊 Service type: {type(service)}")
        
        # Test 3: Test basic functionality
        logger.info("🧪 Testing basic functionality...")
        if hasattr(service, 'scrape_facebook_post_uids'):
            logger.info("✅ scrape_facebook_post_uids method exists")
        else:
            logger.error("❌ scrape_facebook_post_uids method missing")
        
        return service
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return None

async def test_api_function():
    """Test the API function directly"""
    logger.info("🧪 Testing API function...")
    
    try:
        # Import the API function
        from api.scraping import get_facebook_scraper_service
        
        logger.info("📦 API function imported successfully")
        
        # Test the function
        service = await get_facebook_scraper_service()
        
        if service:
            logger.info("✅ API function returned service successfully")
            logger.info(f"📊 Service type: {type(service)}")
        else:
            logger.error("❌ API function returned None")
            
        return service
        
    except Exception as e:
        logger.error(f"❌ API function test failed: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return None

async def main():
    """Main test function"""
    logger.info("🚀 Starting FacebookScraperService Debug Tests")
    logger.info("=" * 60)
    
    # Test 1: Direct service creation
    logger.info("\n" + "="*30)
    logger.info("TEST 1: Direct Service Creation")
    logger.info("="*30)
    
    service1 = await test_scraper_service()
    
    # Test 2: API function
    logger.info("\n" + "="*30)
    logger.info("TEST 2: API Function Test")
    logger.info("="*30)
    
    service2 = await test_api_function()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📋 TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Direct Service Creation: {'✅ PASS' if service1 else '❌ FAIL'}")
    logger.info(f"API Function Test: {'✅ PASS' if service2 else '❌ FAIL'}")
    
    if service1 and service2:
        logger.info("🎉 All tests passed! Service is working correctly.")
    else:
        logger.error("❌ Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    # Run tests
    asyncio.run(main())
