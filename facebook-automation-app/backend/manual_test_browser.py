#!/usr/bin/env python3
"""
Manual test to verify browser actually opens and is visible
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
from pathlib import Path

async def manual_browser_test():
    """Manual test that waits for user confirmation"""
    print("🧪 Manual Browser Visibility Test")
    print("=" * 50)
    
    try:
        # Create a test profile
        profile_data = {
            "name": f"Manual Test Profile {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created test profile: {profile['name']}")
                    print(f"   Profile ID: {profile_id}")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "987654321"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return False
            
            # Create mock cookies directory
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create a mock cookies file
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock facebook cookies for manual test")
            print(f"✅ Created mock cookies file")
            
            print("\n🚀 About to launch browser...")
            print("   Watch your screen for a new Chrome window!")
            print("   The browser should:")
            print("   - Open a new Chrome window")
            print("   - Navigate to https://www.facebook.com/")
            print("   - Display the Facebook page")
            print("   - Stay open for inspection")
            
            input("\nPress Enter to launch browser...")
            
            # Test the check profile endpoint
            print("🚀 Launching browser...")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ API Response: {result.get('status')}")
                    print(f"   Message: {result.get('message')}")
                    
                    if result.get('status') == 'browser_launched':
                        print("\n⏳ Waiting for browser to start...")
                        await asyncio.sleep(5)
                        
                        print("\n❓ MANUAL VERIFICATION:")
                        print("   1. Can you see a new Chrome window?")
                        print("   2. Did it navigate to Facebook?")
                        print("   3. Is the Facebook page displayed?")
                        print("   4. Is the browser window staying open?")
                        
                        while True:
                            answer = input("\nDid the browser open and navigate to Facebook? (y/n/retry): ").lower().strip()
                            
                            if answer == 'y':
                                print("✅ SUCCESS: Browser opened and navigated to Facebook!")
                                
                                # Wait for user to inspect
                                input("\nPress Enter when you're done inspecting the browser...")
                                
                                # Clean up
                                await cleanup_profile(session, profile_id, profile_dir)
                                return True
                                
                            elif answer == 'n':
                                print("❌ FAILED: Browser did not open or navigate properly")
                                
                                # Get more details
                                print("\nWhat happened?")
                                print("1. No browser window opened")
                                print("2. Browser opened but didn't navigate to Facebook")
                                print("3. Browser opened but closed immediately")
                                print("4. Other issue")
                                
                                issue = input("Enter issue number (1-4): ").strip()
                                print(f"Issue reported: {issue}")
                                
                                # Clean up
                                await cleanup_profile(session, profile_id, profile_dir)
                                return False
                                
                            elif answer == 'retry':
                                print("🔄 Retrying browser launch...")
                                async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as retry_resp:
                                    if retry_resp.status == 200:
                                        retry_result = await retry_resp.json()
                                        print(f"✅ Retry API Response: {retry_result.get('status')}")
                                        await asyncio.sleep(3)
                                    else:
                                        print(f"❌ Retry failed: {retry_resp.status}")
                            else:
                                print("Please enter 'y', 'n', or 'retry'")
                    else:
                        print(f"❌ Unexpected status: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        await cleanup_profile(session, profile_id, profile_dir)
                        return False
                else:
                    error_text = await resp.text()
                    print(f"❌ API call failed: {resp.status} - {error_text}")
                    await cleanup_profile(session, profile_id, profile_dir)
                    return False
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def cleanup_profile(session, profile_id, profile_dir):
    """Clean up test profile"""
    try:
        # Delete test profile
        async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
            if resp.status == 200:
                print("✅ Cleaned up test profile")
            else:
                print(f"⚠️ Failed to clean up profile: {resp.status}")
        
        # Clean up cookies directory
        if profile_dir.exists():
            import shutil
            shutil.rmtree(profile_dir)
            print("✅ Cleaned up cookies directory")
            
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")

if __name__ == "__main__":
    print("🔍 Manual Browser Test")
    print("This test will help verify that the browser actually opens and is visible.")
    print("Make sure you can see your screen and are ready to observe browser behavior.")
    
    input("\nPress Enter to start the test...")
    
    success = asyncio.run(manual_browser_test())
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MANUAL TEST PASSED!")
        print("   ✅ Browser opens correctly")
        print("   ✅ Navigates to Facebook")
        print("   ✅ Check button functionality working")
    else:
        print("💥 MANUAL TEST FAILED!")
        print("   ❌ Browser launch issue detected")
        print("   🔧 Further investigation needed")
    
    print("\nTest completed.")
