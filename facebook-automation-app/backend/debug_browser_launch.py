#!/usr/bin/env python3
"""
Debug script to test browser launch functionality
"""

import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.zendriver_service import ZendriverService
from app.services.browser_service import BrowserService
from app.models.profile import Profile

async def debug_browser_launch():
    """Debug the browser launch process step by step"""
    print("🔍 Debugging browser launch process...")
    
    try:
        # Create a mock profile for testing
        profile = Profile(
            id="debug-profile-123",
            name="Debug Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US",
            proxy_type="no_proxy",
            facebook_email="<EMAIL>",
            facebook_user_id="123456789"
        )
        
        print(f"✓ Created test profile: {profile.name}")
        
        # Create mock cookies
        browser_service = BrowserService()
        browser_config = browser_service.create_browser_config(profile)
        profile_path = browser_config["profile_path"]
        cookies_file = os.path.join(profile_path, "Default", "Cookies")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(cookies_file), exist_ok=True)
        
        # Create mock cookies file
        with open(cookies_file, 'w') as f:
            f.write("mock cookies data")
        
        print(f"✓ Created mock cookies at: {cookies_file}")
        
        # Test zendriver config
        zendriver_config = browser_service.get_zendriver_config(profile)
        print(f"✓ Zendriver config: {zendriver_config}")
        
        # Validate config
        is_valid, message = browser_service.validate_zendriver_config(zendriver_config)
        print(f"✓ Config validation: {is_valid} - {message}")
        
        if not is_valid:
            print(f"❌ Invalid config, stopping debug")
            return False
        
        # Test zendriver service
        zendriver_service = ZendriverService()
        
        # Check session directory
        session_dir = zendriver_service._get_session_directory(profile)
        print(f"✓ Session directory: {session_dir}")
        
        # Create and examine the script
        script_content = zendriver_service._create_check_profile_script(profile, zendriver_config)
        script_path = os.path.join(session_dir, "debug_check_profile.py")
        
        os.makedirs(session_dir, exist_ok=True)
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✓ Created script at: {script_path}")
        print(f"✓ Script content preview:")
        print("=" * 50)
        print(script_content[:500] + "..." if len(script_content) > 500 else script_content)
        print("=" * 50)
        
        # Test script execution manually
        print("\n🚀 Testing script execution manually...")
        
        # Run the script directly
        import subprocess
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True, 
                              cwd=session_dir,
                              timeout=30)
        
        print(f"✓ Script execution result:")
        print(f"   Return code: {result.returncode}")
        print(f"   STDOUT: {result.stdout}")
        print(f"   STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Script executed successfully!")
            return True
        else:
            print(f"❌ Script execution failed with code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_zendriver():
    """Test zendriver directly without subprocess"""
    print("\n🔬 Testing zendriver directly...")
    
    try:
        # Import zendriver
        zendriver_path = os.path.join(os.path.dirname(__file__), "zendriver_local")
        if zendriver_path not in sys.path:
            sys.path.insert(0, zendriver_path)
        
        import zendriver as zd
        print("✓ Zendriver imported successfully")
        
        # Create temp profile
        temp_dir = tempfile.mkdtemp(prefix="zendriver_test_")
        print(f"✓ Created temp profile dir: {temp_dir}")
        
        # Test basic browser launch
        config = {
            "user_data_dir": temp_dir,
            "headless": False,
            "sandbox": False,
            "browser_connection_timeout": 10.0,
            "browser_connection_max_tries": 5,
        }
        
        print("🚀 Launching browser with zendriver...")
        browser = await zd.start(**config)
        print("✅ Browser launched successfully!")
        
        # Navigate to a test page
        page = await browser.get("https://www.google.com")
        print("✅ Navigation successful!")
        
        # Wait a moment
        await asyncio.sleep(3)
        
        # Close browser
        await browser.stop()
        print("✅ Browser closed successfully!")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Direct zendriver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Starting browser launch debugging...")
    print("=" * 60)
    
    # Test 1: Debug the full process
    success1 = asyncio.run(debug_browser_launch())
    
    # Test 2: Test zendriver directly
    success2 = asyncio.run(test_direct_zendriver())
    
    print("\n" + "=" * 60)
    print("📊 Debug Results:")
    print(f"   Full process test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Direct zendriver test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("✅ All tests passed - browser launch should work!")
    else:
        print("❌ Some tests failed - need to investigate further")
