#!/usr/bin/env python3
"""
Complete test runner for Facebook automation system
"""
import asyncio
import subprocess
import time
import sys
import os
import signal
from pathlib import Path

def start_server():
    """Start the FastAPI server"""
    print("Starting FastAPI server...")
    
    # Start server in background
    process = subprocess.Popen(
        [sys.executable, "main.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(10)
    
    return process

async def run_workflow_tests():
    """Run the workflow tests"""
    print("\n" + "="*60)
    print("RUNNING FACEBOOK AUTOMATION WORKFLOW TESTS")
    print("="*60)
    
    # Run component tests
    print("\n1. Running component tests...")
    component_result = subprocess.run(
        [sys.executable, "test_facebook_workflow.py"],
        capture_output=True,
        text=True
    )
    
    print("Component test output:")
    print(component_result.stdout)
    if component_result.stderr:
        print("Component test errors:")
        print(component_result.stderr)
    
    component_success = component_result.returncode == 0
    print(f"Component tests: {'✓ PASS' if component_success else '✗ FAIL'}")
    
    # Run API tests
    print("\n2. Running API tests...")
    api_result = subprocess.run(
        [sys.executable, "test_api_workflow.py"],
        capture_output=True,
        text=True
    )
    
    print("API test output:")
    print(api_result.stdout)
    if api_result.stderr:
        print("API test errors:")
        print(api_result.stderr)
    
    api_success = api_result.returncode == 0
    print(f"API tests: {'✓ PASS' if api_success else '✗ FAIL'}")
    
    return component_success, api_success

def main():
    """Main test runner"""
    print("=== FACEBOOK AUTOMATION COMPLETE TEST SUITE ===")
    print("This will test the entire Facebook automation system")
    print("including browser launching, Facebook login, and scraping")
    
    server_process = None
    
    try:
        # Start server
        server_process = start_server()
        
        # Check if server started successfully
        if server_process.poll() is not None:
            stdout, stderr = server_process.communicate()
            print(f"Server failed to start:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return
        
        print("✓ Server started successfully")
        
        # Run tests
        component_success, api_success = asyncio.run(run_workflow_tests())
        
        # Summary
        print("\n" + "="*60)
        print("COMPLETE TEST SUITE RESULTS:")
        print("="*60)
        print(f"Component Tests: {'✓ PASS' if component_success else '✗ FAIL'}")
        print(f"API Tests:       {'✓ PASS' if api_success else '✗ FAIL'}")
        print("="*60)
        
        if component_success and api_success:
            print("🎉 ALL TESTS PASSED! Facebook automation system is ready!")
            print("\nNext steps:")
            print("1. Start the frontend application")
            print("2. Create profiles with Facebook credentials")
            print("3. Login to Facebook using the 'Facebook Login' button")
            print("4. Create scraping sessions and start scraping")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            print("\nCommon issues:")
            print("- Chrome browser not installed")
            print("- Zendriver dependencies missing")
            print("- Database connection issues")
            print("- Network connectivity problems")
    
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    except Exception as e:
        print(f"Test runner error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Stop server
        if server_process and server_process.poll() is None:
            print("\nStopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
            print("✓ Server stopped")

if __name__ == "__main__":
    main()
