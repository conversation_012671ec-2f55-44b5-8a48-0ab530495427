#!/usr/bin/env python3
"""
Integration test for frontend Check button functionality
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
from pathlib import Path

async def create_test_profile_with_cookies():
    """Create a test profile with mock cookies for frontend testing"""
    print("🧪 Creating test profile with cookies for frontend testing...")
    
    try:
        # Create a test profile
        profile_data = {
            "name": f"Frontend Test Profile {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created test profile: {profile['name']} (ID: {profile_id})")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return None
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "987654321"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return None
            
            # Create mock cookies directory
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create a mock cookies file
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock facebook cookies data for frontend test")
            print(f"✅ Created mock cookies file: {cookies_file}")
            
            print(f"\n🎯 Test profile ready for frontend testing!")
            print(f"   Profile ID: {profile_id}")
            print(f"   Profile Name: {profile['name']}")
            print(f"   Facebook Email: <EMAIL>")
            print(f"   Status: Ready for Check button test")
            print(f"\n📋 Instructions:")
            print(f"   1. Go to http://localhost:3001")
            print(f"   2. Navigate to Profiles page")
            print(f"   3. Find profile: {profile['name']}")
            print(f"   4. Click the 'Check' button")
            print(f"   5. Verify that antidetect browser opens and navigates to Facebook")
            print(f"   6. Check that the success message appears")
            
            return profile_id
                    
    except Exception as e:
        print(f"❌ Test setup failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None

async def cleanup_test_profile(profile_id):
    """Clean up test profile"""
    if not profile_id:
        return
        
    try:
        async with aiohttp.ClientSession() as session:
            # Delete test profile
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print(f"✅ Cleaned up test profile: {profile_id}")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            if profile_dir.exists():
                import shutil
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
                
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")

async def main():
    """Main test function"""
    print("🚀 Setting up frontend integration test for Check button...")
    print("=" * 60)
    
    # Create test profile
    profile_id = await create_test_profile_with_cookies()
    
    if profile_id:
        print("\n" + "=" * 60)
        print("✅ Test setup complete!")
        print("🌐 Frontend is running at: http://localhost:3001")
        print("🔧 Backend is running at: http://localhost:8000")
        print("\nPress Enter when you're done testing to clean up...")
        input()
        
        # Cleanup
        print("\n🧹 Cleaning up test data...")
        await cleanup_test_profile(profile_id)
        print("✅ Cleanup complete!")
    else:
        print("❌ Test setup failed!")

if __name__ == "__main__":
    asyncio.run(main())
