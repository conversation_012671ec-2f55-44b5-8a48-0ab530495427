#!/usr/bin/env python3
"""
Simple test to verify zendriver browser launch
"""
import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add zendriver to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "zendriver_local"))

async def test_simple_browser():
    """Test simple browser launch"""
    try:
        print("Testing simple zendriver browser launch...")
        
        # Import zendriver
        import zendriver as zd
        print("✓ Zendriver imported successfully")
        
        # Create temp profile directory
        temp_dir = tempfile.mkdtemp(prefix="test_browser_")
        print(f"Using temp directory: {temp_dir}")
        
        # Simple config
        config = {
            "user_data_dir": temp_dir,
            "headless": False,
            "sandbox": False,
            "browser_executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        }
        
        print("Starting browser...")
        browser = await zd.start(**config)
        print("✓ Browser started")
        
        # Navigate to Google
        page = await browser.get("https://www.google.com")
        print("✓ Navigated to Google")
        
        # Wait a moment
        await asyncio.sleep(3)
        
        # Get title
        title = await page.evaluate("document.title")
        print(f"✓ Page title: {title}")
        
        # Close browser
        await browser.stop()
        print("✓ Browser closed")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("✓ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_facebook_navigation():
    """Test navigation to Facebook"""
    try:
        print("\nTesting Facebook navigation...")
        
        import zendriver as zd
        
        # Create temp profile directory
        temp_dir = tempfile.mkdtemp(prefix="test_facebook_")
        print(f"Using temp directory: {temp_dir}")
        
        # Config with antidetect settings
        config = {
            "user_data_dir": temp_dir,
            "headless": False,
            "sandbox": False,
            "browser_executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "browser_args": [
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--no-first-run",
                "--disable-dev-shm-usage",
                "--remote-allow-origins=*"
            ]
        }
        
        print("Starting browser with antidetect settings...")
        browser = await zd.start(**config)
        print("✓ Browser started")
        
        # Navigate to Facebook login
        page = await browser.get("https://www.facebook.com/login")
        print("✓ Navigated to Facebook login")
        
        # Set title
        await page.evaluate("""
            document.title = 'Facebook Login Test - Manual login test';
        """)
        print("✓ Page title set")
        
        # Wait for manual inspection
        print("Browser is ready. You can manually check Facebook login page.")
        print("Press Ctrl+C to close...")
        
        try:
            await asyncio.sleep(30)  # Wait 30 seconds
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
        
        # Close browser
        await browser.stop()
        print("✓ Browser closed")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("✓ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=== Simple Browser Test ===\n")
    
    # Test 1: Simple browser launch
    simple_success = await test_simple_browser()
    
    if simple_success:
        # Test 2: Facebook navigation
        facebook_success = await test_facebook_navigation()
        
        if facebook_success:
            print("\n✓ All tests passed! Zendriver is working correctly.")
        else:
            print("\n✗ Facebook test failed.")
    else:
        print("\n✗ Simple browser test failed.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
