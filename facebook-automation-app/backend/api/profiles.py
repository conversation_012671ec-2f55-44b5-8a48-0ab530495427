"""
Profile management API endpoints - Unified async version
"""

import logging
import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field, field_validator

# Import async database
from app.core.database import get_db
from app.core.security import security_manager
from app.models.profile import Profile, ProfileStatus, ProxyType

# Import schemas
from schemas.profile import (
    ProfileCreate, ProfileUpdate, ProfileResponse, ProfileList, ProfileStats,
    BrowserSessionStatus, FacebookLoginResponse
)

# Import services
try:
    from app.services.browser_service import BrowserService
    from app.services.facebook_service import FacebookService
    from app.services.zendriver_service import ZendriverService
    browser_service = BrowserService()
    facebook_service = FacebookService()
    zendriver_service = ZendriverService()
except ImportError:
    # Mock services for compatibility
    class MockBrowserService:
        def create_browser_config(self, profile): return {"profile_path": f"/tmp/profile_{profile.id}"}
        def save_browser_config(self, profile, config): pass
        def validate_browser_config(self, config): return True, "Valid"
        def get_zendriver_config(self, profile): return {"user_data_dir": f"/tmp/profile_{profile.id}"}

    class MockFacebookService:
        def validate_credentials(self, email, password): return True, "Valid"
        async def initiate_login(self, profile):
            return {"status": "mock_initiated", "message": "Mock Facebook login initiated"}

    class MockZendriverService:
        async def launch_browser_for_facebook_login(self, profile):
            return {"status": "mock_launched", "message": "Mock browser launched"}

    browser_service = MockBrowserService()
    facebook_service = MockFacebookService()
    zendriver_service = MockZendriverService()

router = APIRouter(prefix="/api/profiles", tags=["profiles"])
logger = logging.getLogger(__name__)


def serialize_datetime(dt):
    """Convert datetime object to ISO string format"""
    if dt is None:
        return None
    if isinstance(dt, datetime):
        return dt.isoformat()
    return dt


def serialize_profile_response(profile: Profile) -> dict:
    """Convert Profile model to ProfileResponse dict with proper datetime serialization"""
    return {
        "id": profile.id,
        "name": profile.name,
        "description": profile.description,
        "status": profile.status,
        "user_agent": profile.user_agent,
        "screen_resolution": profile.screen_resolution,
        "timezone": profile.timezone,
        "language": profile.language,
        "profile_path": profile.profile_path,
        "last_used_at": serialize_datetime(profile.last_used),
        "created_at": serialize_datetime(profile.created_at),
        "updated_at": serialize_datetime(profile.updated_at),
        "login_count": profile.login_count or 0,
        "message_sent_count": profile.message_sent_count or 0,
        "facebook_email": profile.facebook_email,
        "facebook_user_id": profile.facebook_user_id,
        "facebook_username": profile.facebook_username,
        "proxy_host": profile.proxy_host,
        "proxy_port": profile.proxy_port,
        "proxy_type": profile.proxy_type if profile.proxy_type else None,
        "proxy_config": profile.get_proxy_config(),  # Use method instead of attribute
    }

@router.get("/", response_model=List[ProfileResponse])
async def get_profiles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """Get list of profiles with optional filtering."""
    try:
        query = select(Profile)

        if status:
            query = query.where(Profile.status == status)

        query = query.offset(skip).limit(limit).order_by(Profile.created_at.desc())
        result = await db.execute(query)
        profiles = result.scalars().all()

        # Serialize profiles with proper datetime handling
        serialized_profiles = [serialize_profile_response(profile) for profile in profiles]
        return serialized_profiles
    except Exception as e:
        logger.error(f"Error fetching profiles: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profiles")

@router.get("/list", response_model=ProfileList)
async def get_profiles_list(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """Get all profiles with pagination and filtering (legacy endpoint)"""
    try:
        query = select(Profile)

        if status:
            query = query.where(Profile.status == status)

        # Get total count
        count_query = select(func.count(Profile.id))
        if status:
            count_query = count_query.where(Profile.status == status)
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # Get profiles
        query = query.offset(skip).limit(limit).order_by(Profile.created_at.desc())
        result = await db.execute(query)
        profiles = result.scalars().all()

        # Serialize profiles with proper datetime handling
        serialized_profiles = [serialize_profile_response(profile) for profile in profiles]

        return ProfileList(
            profiles=serialized_profiles,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching profiles: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profiles")

@router.get("/stats", response_model=ProfileStats)
async def get_profile_stats(db: AsyncSession = Depends(get_db)):
    """Get profile statistics"""
    try:
        # Get total count
        total_result = await db.execute(select(func.count(Profile.id)))
        total_profiles = total_result.scalar()

        # Get active count
        active_result = await db.execute(select(func.count(Profile.id)).where(Profile.status == "active"))
        active_profiles = active_result.scalar()

        # Get inactive count
        inactive_result = await db.execute(select(func.count(Profile.id)).where(Profile.status == "inactive"))
        inactive_profiles = inactive_result.scalar()

        # Get banned count
        banned_result = await db.execute(select(func.count(Profile.id)).where(Profile.status == "banned"))
        banned_profiles = banned_result.scalar()

        # Get error count
        error_result = await db.execute(select(func.count(Profile.id)).where(Profile.status == "error"))
        error_profiles = error_result.scalar()

        return ProfileStats(
            total_profiles=total_profiles,
            active_profiles=active_profiles,
            inactive_profiles=inactive_profiles,
            banned_profiles=banned_profiles,
            error_profiles=error_profiles
        )
    except Exception as e:
        logger.error(f"Error fetching profile stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile statistics")

@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get a specific profile by ID"""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile")

@router.post("/", response_model=ProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_profile(profile_data: ProfileCreate, db: AsyncSession = Depends(get_db)):
    """Create new profile with browser and proxy configuration."""
    try:
        # Check if name already exists
        existing = await db.execute(select(Profile).where(Profile.name == profile_data.name))
        if existing.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile name already exists"
            )

        # Prepare browser configuration from profile data
        browser_config = {
            "user_agent": profile_data.user_agent,
            "screen_resolution": profile_data.screen_resolution,
            "timezone": profile_data.timezone,
            "language": profile_data.language
        }
        # Remove None values
        browser_config = {k: v for k, v in browser_config.items() if v is not None}

        # Create profile with all fields
        profile = Profile(
            id=str(uuid.uuid4()),
            name=profile_data.name,
            description=profile_data.description,
            status="active",
            user_agent=profile_data.user_agent,
            screen_resolution=profile_data.screen_resolution,
            timezone=profile_data.timezone,
            language=profile_data.language,
            facebook_email=profile_data.facebook_email,
            facebook_password=profile_data.facebook_password,
            facebook_username=profile_data.facebook_username,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Set proxy configuration directly in profile
        if hasattr(profile_data, 'proxy_type') and profile_data.proxy_type:
            profile.proxy_type = profile_data.proxy_type
            profile.proxy_host = profile_data.proxy_host
            profile.proxy_port = profile_data.proxy_port
            profile.proxy_username = profile_data.proxy_username
            profile.proxy_password = profile_data.proxy_password

        # Set browser config using the model method
        if browser_config:
            profile.set_browser_config(browser_config)

        db.add(profile)
        await db.flush()  # Get profile ID

        await db.commit()

        # Save browser configuration file
        try:
            final_config = browser_service.create_browser_config(profile)
            browser_service.save_browser_config(profile, final_config)
        except Exception as e:
            # Log error but don't fail the profile creation
            logger.warning(f"Failed to save browser config for profile {profile.id}: {e}")

        logger.info(f"Created new profile: {profile.name} ({profile.id})")
        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating profile: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create profile")

@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: str,
    profile_data: ProfileUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update an existing profile"""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Update profile fields
        update_data = profile_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(profile, field):
                setattr(profile, field, value)

        # Update timestamp
        profile.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(profile)

        logger.info(f"Updated profile: {profile.name} ({profile.id})")
        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update profile")

@router.delete("/{profile_id}")
async def delete_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Delete a profile"""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Check if profile is being used in active campaigns
        # TODO: Add check for active campaigns

        await db.delete(profile)
        await db.commit()

        logger.info(f"Deleted profile: {profile.name} ({profile.id})")
        return {"message": "Profile deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete profile")


@router.post("/{profile_id}/facebook-login", response_model=FacebookLoginResponse)
async def facebook_login(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Initiate Facebook login for profile - Launch antidetect browser."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Update profile status to indicate login in progress
        profile.status = ProfileStatus.ACTIVE
        profile.last_used = datetime.utcnow()
        await db.commit()

        logger.info(f"Facebook login initiated for profile: {profile.name} ({profile.id})")

        # Launch antidetect browser using FacebookService
        try:
            browser_result = await facebook_service.initiate_login(profile)

            if browser_result.get("status") == "login_initiated":
                return {
                    "status": "browser_launched",
                    "message": "Antidetect browser launched successfully. Complete login manually in the browser window.",
                    "profile_id": profile.id,
                    "profile_name": profile.name,
                    "session_id": browser_result.get("session_id"),
                    "browser_status": "browser_launched",
                    "login_url": "https://www.facebook.com/login",
                    "instructions": browser_result.get("instructions", [
                        "1. Browser window has opened with your antidetect profile configuration",
                        "2. Facebook login page should load automatically",
                        "3. Enter your Facebook credentials manually",
                        "4. Complete any 2FA or security checks if prompted",
                        "5. Once logged in successfully, click 'Complete Login' button in the app",
                        "6. Browser will remain open until login is completed or timeout occurs"
                    ])
                }
            elif browser_result.get("status") == "session_exists":
                return {
                    "status": "session_exists",
                    "message": "Browser session already active for this profile. Complete login in existing browser window.",
                    "profile_id": profile.id,
                    "profile_name": profile.name,
                    "session_id": browser_result.get("session_id"),
                    "browser_status": "active",
                    "login_url": "https://www.facebook.com/login",
                    "instructions": [
                        "1. Browser window is already open for this profile",
                        "2. Complete Facebook login in the existing browser window",
                        "3. Once logged in, click 'Complete Login' button in the app"
                    ]
                }
            elif browser_result.get("status") == "error":
                # Handle error from FacebookService
                logger.error(f"FacebookService error for profile {profile_id}: {browser_result.get('message')}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to launch browser: {browser_result.get('message', 'Unknown error')}"
                )
            else:
                # Unknown status
                logger.warning(f"Unknown browser result status: {browser_result}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Unexpected login result: {browser_result.get('status', 'unknown')}"
                )

        except HTTPException:
            raise
        except Exception as browser_error:
            logger.error(f"Browser launch error for profile {profile_id}: {browser_error}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to launch browser: {str(browser_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initiating Facebook login for profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to initiate Facebook login")


@router.post("/{profile_id}/facebook-login-complete")
async def facebook_login_complete(
    profile_id: str,
    facebook_data: dict = None,
    db: AsyncSession = Depends(get_db)
):
    """Complete Facebook login and update profile status."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Import facebook session handler to extract cookies
        from automation.facebook_session import FacebookSessionHandler
        from automation.browser_manager import BrowserManager
        browser_manager = BrowserManager()
        facebook_session_handler = FacebookSessionHandler(browser_manager)

        # Try to extract cookies from active browser session
        cookies_extracted = False
        try:
            session_info = await facebook_session_handler.get_session_info(profile_id)
            if session_info and session_info.get("cookies"):
                cookies = session_info["cookies"]
                logger.info(f"Extracted {len(cookies)} cookies from browser session for profile {profile_id}")
                cookies_extracted = True
            else:
                logger.warning(f"No cookies found in browser session for profile {profile_id}")
        except Exception as cookie_error:
            logger.error(f"Failed to extract cookies for profile {profile_id}: {cookie_error}")

        # Update profile with Facebook login information
        if facebook_data:
            if facebook_data.get('email'):
                profile.facebook_email = facebook_data['email']
            if facebook_data.get('user_id'):
                profile.facebook_user_id = facebook_data['user_id']
            if facebook_data.get('username'):
                profile.facebook_username = facebook_data['username']

        # Mark profile as Facebook logged in
        profile.facebook_logged_in = True
        profile.status = ProfileStatus.ACTIVE
        profile.last_used = datetime.utcnow()
        await db.commit()

        logger.info(f"Facebook login completed for profile: {profile.name} ({profile.id})")

        return {
            "status": "login_complete",
            "message": "Facebook login completed successfully. Profile is now ready for automation.",
            "profile_id": profile.id,
            "profile_name": profile.name,
            "profile_status": profile.status,
            "facebook_email": profile.facebook_email,
            "facebook_username": profile.facebook_username,
            "facebook_logged_in": profile.facebook_logged_in,
            "cookies_extracted": cookies_extracted
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing Facebook login for profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to complete Facebook login")


@router.get("/{profile_id}/facebook-status")
async def get_facebook_status(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get Facebook login status for profile."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        is_logged_in = bool(profile.facebook_email or profile.facebook_user_id)

        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "is_logged_in": is_logged_in,
            "facebook_email": getattr(profile, 'facebook_email', None),
            "facebook_username": getattr(profile, 'facebook_username', None),
            "facebook_user_id": getattr(profile, 'facebook_user_id', None),
            "last_used": profile.last_used.isoformat() if profile.last_used else None,
            "status": profile.status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Facebook status for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get Facebook status")


@router.post("/{profile_id}/test-proxy")
async def test_proxy_connection(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Test proxy connection for profile."""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Test proxy connection (mock implementation for now)
        return {
            "status": "success",
            "message": "Proxy test completed",
            "response_time": 0.5,
            "working": True,
            "proxy_type": profile.proxy_type or "no_proxy"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing proxy for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to test proxy")


@router.post("/{profile_id}/test-browser")
async def test_browser_configuration(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Test browser configuration for profile."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Test browser configuration using browser service
        try:
            config = browser_service.get_zendriver_config(profile)
            is_valid, message = browser_service.validate_browser_config(config)

            return {
                "status": "success" if is_valid else "error",
                "message": message,
                "ready_for_automation": is_valid,
                "config": config
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Browser test failed: {str(e)}",
                "ready_for_automation": False
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing browser for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to test browser")


@router.post("/{profile_id}/check")
async def check_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Check profile by opening antidetect browser with saved Facebook cookies."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Check if profile has Facebook login info
        if not profile.facebook_email and not profile.facebook_user_id:
            return {
                "status": "no_cookies",
                "message": "No Facebook login information found. Please login to Facebook first.",
                "profile_id": profile_id,
                "profile_name": profile.name
            }

        # Launch browser with saved cookies using Facebook service
        try:
            result = await facebook_service.check_profile_with_cookies(profile)
            return result
        except Exception as e:
            logger.error(f"Error checking profile {profile_id}: {e}")
            return {
                "status": "error",
                "message": f"Failed to check profile: {str(e)}",
                "profile_id": profile_id,
                "profile_name": profile.name
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to check profile")


@router.get("/{profile_id}/zendriver-config")
async def get_zendriver_config(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get zendriver configuration for profile."""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Get zendriver config using browser service
        try:
            config = browser_service.get_zendriver_config(profile)

            return {
                "profile_id": profile.id,
                "profile_name": profile.name,
                "zendriver_config": config,
                "ready_for_automation": True
            }
        except Exception as e:
            logger.error(f"Error generating zendriver config: {e}")
            return {
                "profile_id": profile.id,
                "profile_name": profile.name,
                "zendriver_config": {
                    "user_data_dir": f"/tmp/profile_{profile.id}",
                    "headless": False,
                    "disable_blink_features": "AutomationControlled"
                },
                "ready_for_automation": False,
                "error": str(e)
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting zendriver config for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get zendriver config")


# New advanced endpoints from app/api
@router.post("/{profile_id}/launch-browser")
async def launch_browser(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Launch browser with profile configuration."""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Launch browser using browser service
        try:
            config = browser_service.get_zendriver_config(profile)

            return {
                "status": "success",
                "message": "Browser launch initiated",
                "profile_id": profile.id,
                "config": config,
                "instructions": [
                    "Browser will launch with your profile configuration",
                    "Use the browser for manual Facebook login or automation",
                    "Close browser when finished"
                ]
            }
        except Exception as e:
            logger.error(f"Error launching browser: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to launch browser: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error launching browser for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to launch browser")


@router.post("/{profile_id}/validate")
async def validate_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Validate profile configuration."""
    try:
        # Get profile by ID
        query = select(Profile).where(Profile.id == profile_id)
        result = await db.execute(query)
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        validation_results = {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "validations": {},
            "overall_status": "valid",
            "errors": [],
            "warnings": []
        }

        # Validate browser config
        try:
            config = browser_service.get_zendriver_config(profile)
            is_valid, message = browser_service.validate_browser_config(config)
            validation_results["validations"]["browser"] = {
                "status": "valid" if is_valid else "invalid",
                "message": message
            }
            if not is_valid:
                validation_results["errors"].append(f"Browser: {message}")
                validation_results["overall_status"] = "invalid"
        except Exception as e:
            validation_results["validations"]["browser"] = {
                "status": "error",
                "message": str(e)
            }
            validation_results["errors"].append(f"Browser: {str(e)}")
            validation_results["overall_status"] = "invalid"

        # Validate Facebook credentials
        if profile.facebook_email and profile.facebook_password:
            try:
                is_valid, message = facebook_service.validate_credentials(
                    profile.facebook_email,
                    profile.facebook_password
                )
                validation_results["validations"]["facebook"] = {
                    "status": "valid" if is_valid else "invalid",
                    "message": message
                }
                if not is_valid:
                    validation_results["warnings"].append(f"Facebook: {message}")
            except Exception as e:
                validation_results["validations"]["facebook"] = {
                    "status": "error",
                    "message": str(e)
                }
                validation_results["warnings"].append(f"Facebook: {str(e)}")
        else:
            validation_results["validations"]["facebook"] = {
                "status": "missing",
                "message": "Facebook credentials not provided"
            }
            validation_results["warnings"].append("Facebook credentials not provided")

        # Validate proxy config
        if profile.proxy_config:
            validation_results["validations"]["proxy"] = {
                "status": "configured",
                "message": f"Proxy configured: {profile.proxy_config.proxy_type}"
            }
        else:
            validation_results["validations"]["proxy"] = {
                "status": "none",
                "message": "No proxy configured (using direct connection)"
            }

        return validation_results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to validate profile")


@router.get("/proxy-types")
async def get_proxy_types():
    """Get available proxy types with recommendations."""
    return {
        "proxy_types": {
            "no_proxy": {
                "name": "No Proxy",
                "value": "no_proxy",
                "recommendations": {
                    "description": "Direct connection without proxy",
                    "use_cases": ["Local development", "No anonymity required"],
                    "security_level": "Low",
                    "speed": "Fast"
                }
            },
            "http": {
                "name": "HTTP",
                "value": "http",
                "recommendations": {
                    "description": "HTTP proxy for web traffic",
                    "use_cases": ["Web browsing", "API requests"],
                    "common_ports": [8080, 3128, 8888, 80],
                    "security_level": "Medium",
                    "speed": "Fast"
                }
            },
            "https": {
                "name": "HTTPS",
                "value": "https",
                "recommendations": {
                    "description": "HTTPS proxy with SSL encryption",
                    "use_cases": ["Secure web browsing", "Encrypted traffic"],
                    "common_ports": [8080, 3128, 8888, 443],
                    "security_level": "High",
                    "speed": "Medium"
                }
            },
            "socks5": {
                "name": "SOCKS5",
                "value": "socks5",
                "recommendations": {
                    "description": "SOCKS5 proxy supporting all traffic types",
                    "use_cases": ["All protocols", "High anonymity"],
                    "common_ports": [1080, 1081, 9050],
                    "security_level": "High",
                    "speed": "Medium"
                }
            },
            "ssh": {
                "name": "SSH",
                "value": "ssh",
                "recommendations": {
                    "description": "SSH tunnel proxy for maximum security",
                    "use_cases": ["Secure tunneling", "Server access"],
                    "common_ports": [22, 2222],
                    "security_level": "Very High",
                    "speed": "Slow"
                }
            }
        },
        "default": "no_proxy"
    }


# Browser session management endpoints

@router.get("/{profile_id}/browser-session-status")
async def get_browser_session_status(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get browser session status for profile."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Get session status from zendriver service
        session_status = zendriver_service.get_session_status(profile_id)

        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "browser_session": session_status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting browser session status for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get browser session status")


@router.post("/{profile_id}/terminate-browser-session")
async def terminate_browser_session(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Terminate browser session for profile."""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Terminate session using zendriver service
        termination_result = await zendriver_service.terminate_session(profile_id)

        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "termination_result": termination_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error terminating browser session for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to terminate browser session")


@router.get("/browser-sessions")
async def get_all_browser_sessions():
    """Get all active browser sessions."""
    try:
        sessions = zendriver_service.get_all_active_sessions()
        return sessions

    except Exception as e:
        logger.error(f"Error getting all browser sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get browser sessions")
