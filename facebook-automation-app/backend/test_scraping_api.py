"""
Test script for scraping API endpoint
Tests the /api/scraping/sessions/{id}/start endpoint
"""
import asyncio
import aiohttp
import json
import time
from loguru import logger
import sys


class ScrapingAPITester:
    """Test the scraping API endpoints"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_create_session(self):
        """Test creating a scraping session"""
        logger.info("🧪 Testing session creation...")
        
        payload = {
            "name": "API Test Session",
            "post_url": "https://www.facebook.com/groups/591054007361950/posts/8068194536314616/",
            "profile_id": "test_profile",
            "scraping_types": ["comments"]
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/scraping/sessions",
                json=payload
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    session_id = data.get("id")
                    logger.info(f"✅ Session created successfully: ID {session_id}")
                    return session_id
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to create session: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Exception creating session: {e}")
            return None
    
    async def test_start_session(self, session_id):
        """Test starting a scraping session"""
        logger.info(f"🚀 Testing session start for ID: {session_id}")
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/scraping/sessions/{session_id}/start"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Session started successfully:")
                    logger.info(f"   Message: {data.get('message', 'N/A')}")
                    logger.info(f"   Status: {data.get('status', 'N/A')}")
                    logger.info(f"   Profile: {data.get('profile_name', 'N/A')}")
                    logger.info(f"   Step: {data.get('step', 'N/A')}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to start session: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Exception starting session: {e}")
            return False
    
    async def test_get_session_status(self, session_id):
        """Test getting session status"""
        logger.info(f"📊 Testing session status for ID: {session_id}")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/scraping/sessions/{session_id}"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Session status retrieved:")
                    logger.info(f"   ID: {data.get('id', 'N/A')}")
                    logger.info(f"   Name: {data.get('name', 'N/A')}")
                    logger.info(f"   Status: {data.get('status', 'N/A')}")
                    logger.info(f"   Progress: {data.get('progress_percentage', 0)}%")
                    logger.info(f"   Total Found: {data.get('total_found', 0)}")
                    logger.info(f"   Unique Users: {data.get('unique_users', 0)}")
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to get session status: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Exception getting session status: {e}")
            return None
    
    async def monitor_session_progress(self, session_id, max_wait_time=300):
        """Monitor session progress until completion"""
        logger.info(f"👀 Monitoring session {session_id} progress...")
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < max_wait_time:
            status_data = await self.test_get_session_status(session_id)
            
            if status_data:
                current_status = status_data.get('status')
                progress = status_data.get('progress_percentage', 0)
                
                if current_status != last_status:
                    logger.info(f"📈 Status changed: {last_status} → {current_status}")
                    last_status = current_status
                
                logger.info(f"⏳ Progress: {progress}% | Status: {current_status}")
                
                if current_status in ['completed', 'failed', 'stopped']:
                    logger.info(f"🏁 Session finished with status: {current_status}")
                    return status_data
            
            await asyncio.sleep(5)  # Check every 5 seconds
        
        logger.warning(f"⏰ Monitoring timeout after {max_wait_time}s")
        return None
    
    async def test_full_workflow(self):
        """Test the complete scraping workflow"""
        logger.info("🔄 Testing complete scraping workflow...")
        
        # Step 1: Create session
        session_id = await self.test_create_session()
        if not session_id:
            logger.error("❌ Cannot proceed without session ID")
            return False
        
        # Step 2: Start session
        start_success = await self.test_start_session(session_id)
        if not start_success:
            logger.error("❌ Cannot proceed - session start failed")
            return False
        
        # Step 3: Monitor progress
        final_status = await self.monitor_session_progress(session_id)
        
        if final_status:
            logger.info("✅ Full workflow test completed!")
            logger.info(f"📊 Final Results:")
            logger.info(f"   Status: {final_status.get('status', 'N/A')}")
            logger.info(f"   Total Found: {final_status.get('total_found', 0)}")
            logger.info(f"   Unique Users: {final_status.get('unique_users', 0)}")
            return True
        else:
            logger.error("❌ Full workflow test failed")
            return False
    
    async def test_service_health(self):
        """Test if the scraping service is healthy"""
        logger.info("🏥 Testing service health...")

        try:
            # Test basic API endpoint
            async with self.session.get(
                f"{self.base_url}/api/scraping/sessions"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Basic API health check passed")
                    logger.info(f"   Sessions endpoint accessible")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Health check failed: {response.status} - {error_text}")
                    return False

        except Exception as e:
            logger.error(f"❌ Exception in health check: {e}")
            logger.error(f"   Make sure the backend server is running on {self.base_url}")
            return False


async def main():
    """Main test function"""
    logger.info("🚀 Starting Scraping API Tests")
    logger.info("=" * 50)
    
    async with ScrapingAPITester() as tester:
        # Test 1: Service health
        logger.info("\n" + "="*30)
        logger.info("TEST 1: Service Health Check")
        logger.info("="*30)
        
        health_ok = await tester.test_service_health()
        if not health_ok:
            logger.error("❌ Service is not healthy, stopping tests")
            return
        
        # Test 2: Full workflow
        logger.info("\n" + "="*30)
        logger.info("TEST 2: Full Scraping Workflow")
        logger.info("="*30)
        
        workflow_ok = await tester.test_full_workflow()
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("📋 TEST SUMMARY")
        logger.info("="*50)
        logger.info(f"Service Health: {'✅ PASS' if health_ok else '❌ FAIL'}")
        logger.info(f"Full Workflow: {'✅ PASS' if workflow_ok else '❌ FAIL'}")
        
        if health_ok and workflow_ok:
            logger.info("🎉 All tests passed!")
        else:
            logger.error("❌ Some tests failed!")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    # Run tests
    asyncio.run(main())
