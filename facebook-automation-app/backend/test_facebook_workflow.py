#!/usr/bin/env python3
"""
Test script to verify the complete Facebook automation workflow
"""
import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add project paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "zendriver_local"))
sys.path.insert(0, os.path.dirname(__file__))

# Test imports
try:
    import zendriver as zd
    print("✓ Zendriver imported successfully")
except ImportError as e:
    print(f"✗ Failed to import zendriver: {e}")
    sys.exit(1)

try:
    from app.services.zendriver_service import ZendriverService
    from app.services.facebook_service import FacebookService
    from app.services.browser_service import BrowserService
    from app.models.profile import Profile
    print("✓ Services imported successfully")
except ImportError as e:
    print(f"✗ Failed to import services: {e}")
    sys.exit(1)

async def test_zendriver_service():
    """Test ZendriverService directly"""
    print("\n=== Testing ZendriverService ===")
    
    try:
        # Create a mock profile
        profile = Profile(
            id="test-profile-123",
            name="Test Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US"
        )
        
        # Initialize service
        zendriver_service = ZendriverService()
        print("✓ ZendriverService initialized")
        
        # Test browser launch
        result = await zendriver_service.launch_browser_for_facebook_login(profile)
        print(f"Browser launch result: {result}")
        
        if result["status"] == "browser_launched":
            print("✓ Browser launched successfully")
            
            # Wait a moment
            await asyncio.sleep(5)
            
            # Check session status
            status = zendriver_service.get_session_status(profile.id)
            print(f"Session status: {status}")
            
            # Cleanup
            cleanup_result = await zendriver_service.terminate_session(profile.id)
            print(f"Cleanup result: {cleanup_result}")
            
            return True
        else:
            print(f"✗ Browser launch failed: {result}")
            return False
            
    except Exception as e:
        print(f"✗ ZendriverService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_facebook_service():
    """Test FacebookService"""
    print("\n=== Testing FacebookService ===")
    
    try:
        # Create a mock profile
        profile = Profile(
            id="test-profile-456",
            name="Test Profile FB",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US",
            facebook_email="<EMAIL>"
        )
        
        # Initialize service
        facebook_service = FacebookService()
        print("✓ FacebookService initialized")
        
        # Test login initiation
        result = await facebook_service.initiate_login(profile)
        print(f"Login initiation result: {result}")
        
        if result["status"] in ["login_initiated", "browser_launched"]:
            print("✓ Login initiated successfully")
            
            # Wait a moment
            await asyncio.sleep(3)
            
            # Check login status
            status = await facebook_service.check_login_status(profile)
            print(f"Login status: {status}")
            
            return True
        else:
            print(f"✗ Login initiation failed: {result}")
            return False
            
    except Exception as e:
        print(f"✗ FacebookService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_browser_service():
    """Test BrowserService configuration"""
    print("\n=== Testing BrowserService ===")
    
    try:
        # Create a mock profile
        profile = Profile(
            id="test-profile-789",
            name="Test Profile Browser",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US"
        )
        
        # Initialize service
        browser_service = BrowserService()
        print("✓ BrowserService initialized")
        
        # Test configuration generation
        config = browser_service.get_zendriver_config(profile)
        print(f"✓ Zendriver config generated: {len(config)} settings")
        
        # Validate config
        is_valid, message = browser_service.validate_browser_config(config)
        print(f"Config validation: {is_valid} - {message}")
        
        # Test Chrome path detection
        chrome_path = browser_service._get_chrome_executable_path()
        print(f"Chrome path: {chrome_path}")
        
        if os.path.exists(chrome_path):
            print("✓ Chrome executable found")
            return True
        else:
            print("✗ Chrome executable not found")
            return False
            
    except Exception as e:
        print(f"✗ BrowserService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scraping_service():
    """Test FacebookScraperService"""
    print("\n=== Testing FacebookScraperService ===")
    
    try:
        from automation.facebook_scraper_service import FacebookScraperService
        
        # Initialize service
        scraper_service = FacebookScraperService()
        print("✓ FacebookScraperService initialized")
        
        # Test statistics
        stats = scraper_service.get_scraping_statistics()
        print(f"✓ Scraping statistics: {stats}")
        
        # Test UID count
        uid_count = await scraper_service.get_unique_uids_count()
        print(f"✓ Unique UIDs count: {uid_count}")
        
        return True
        
    except Exception as e:
        print(f"✗ FacebookScraperService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=== Facebook Automation Workflow Test ===\n")
    
    results = {}
    
    # Test 1: BrowserService
    results["browser_service"] = await test_browser_service()
    
    # Test 2: ZendriverService
    results["zendriver_service"] = await test_zendriver_service()
    
    # Test 3: FacebookService
    results["facebook_service"] = await test_facebook_service()
    
    # Test 4: FacebookScraperService
    results["scraping_service"] = await test_scraping_service()
    
    # Summary
    print("\n" + "="*50)
    print("TEST RESULTS SUMMARY:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name:20} {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("✓ ALL TESTS PASSED - Facebook automation workflow is ready!")
    else:
        print("✗ SOME TESTS FAILED - Check the errors above")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
