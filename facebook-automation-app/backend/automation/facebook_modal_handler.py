"""
Facebook Modal Handler - Optimized scrolling and interaction for Facebook modals

This module provides specialized handling for Facebook post modals, including:
- Modal detection and identification
- Modal-aware scrolling strategies
- Comment loading optimization
- Cross-browser compatibility
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ModalScrollConfig:
    """Configuration for modal scrolling behavior"""
    scroll_distance: int = 500
    scroll_delay: float = 2.0
    max_scroll_attempts: int = 10
    enable_wheel_events: bool = True
    enable_content_area_detection: bool = True
    enable_adaptive_scrolling: bool = True
    fallback_to_page_scroll: bool = True


class FacebookModalHandler:
    """Handles Facebook modal detection and scrolling optimization"""
    
    def __init__(self, config: ModalScrollConfig = None):
        self.config = config or ModalScrollConfig()
        self.detected_modal = None
        self.scroll_performance_stats = {
            "modal_detections": 0,
            "successful_modal_scrolls": 0,
            "fallback_scrolls": 0,
            "total_scroll_attempts": 0
        }
    
    async def detect_and_configure_modal(self, tab) -> Dict[str, Any]:
        """
        Detect Facebook modal and configure optimal scrolling strategy
        
        Returns:
            Dict with modal info and recommended scrolling approach
        """
        try:
            self.scroll_performance_stats["modal_detections"] += 1
            
            # Step 1: Detect modal container
            modal_info = await self._comprehensive_modal_detection(tab)
            
            if modal_info["detected"]:
                logger.info(f"✅ Modal detected: {modal_info['type']} using {modal_info['selector']}")
                self.detected_modal = modal_info
                
                # Step 2: Analyze modal structure for optimal scrolling
                scroll_strategy = await self._analyze_modal_scroll_strategy(tab, modal_info)
                
                return {
                    "modal_detected": True,
                    "modal_info": modal_info,
                    "scroll_strategy": scroll_strategy,
                    "recommended_approach": "modal_aware_scrolling"
                }
            else:
                logger.info("ℹ️ No modal detected, using standard page scrolling")
                return {
                    "modal_detected": False,
                    "modal_info": None,
                    "scroll_strategy": {"type": "page_scroll"},
                    "recommended_approach": "standard_scrolling"
                }
                
        except Exception as e:
            logger.error(f"❌ Error in modal detection: {e}")
            return {
                "modal_detected": False,
                "error": str(e),
                "recommended_approach": "fallback_scrolling"
            }
    
    async def perform_optimized_scroll(self, tab, scroll_distance: int = None) -> bool:
        """
        Perform optimized scrolling based on detected modal context
        
        Returns:
            bool: True if scroll was successful
        """
        try:
            self.scroll_performance_stats["total_scroll_attempts"] += 1
            distance = scroll_distance or self.config.scroll_distance
            
            if self.detected_modal and self.detected_modal["detected"]:
                # Use modal-aware scrolling
                success = await self._execute_modal_scroll(tab, distance)
                if success:
                    self.scroll_performance_stats["successful_modal_scrolls"] += 1
                    return True
            
            # Fallback to standard scrolling
            if self.config.fallback_to_page_scroll:
                await tab.scroll_down(distance)
                self.scroll_performance_stats["fallback_scrolls"] += 1
                logger.debug(f"Used fallback page scroll: {distance}px")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error in optimized scroll: {e}")
            return False
    
    async def _comprehensive_modal_detection(self, tab) -> Dict[str, Any]:
        """Comprehensive modal detection using multiple strategies"""
        
        # Strategy 1: Standard modal selectors
        standard_selectors = [
            ('div[role="dialog"]', 'aria_dialog'),
            ('div[data-testid="modal-dialog"]', 'facebook_modal'),
            ('div[aria-modal="true"]', 'aria_modal'),
            ('div.uiLayer', 'facebook_ui_layer'),
            ('div._5yd0', 'facebook_legacy_modal'),
            ('div._4t2a', 'facebook_legacy_modal_2')
        ]
        
        for selector, modal_type in standard_selectors:
            try:
                element = await tab.select(selector)
                if element:
                    return {
                        "detected": True,
                        "type": modal_type,
                        "selector": selector,
                        "element": element,
                        "detection_method": "standard_selector"
                    }
            except:
                continue
        
        # Strategy 2: Modern Facebook class-based detection
        modern_selectors = [
            'div.x1n2onr6.x1ja2u2z',  # Modern Facebook modal
            'div[data-pagelet="root"]',  # Facebook pagelet
            'div.x9f619.x1n2onr6.x1ja2u2z'  # Another modern pattern
        ]
        
        for selector in modern_selectors:
            try:
                element = await tab.select(selector)
                if element:
                    # Verify it's actually a modal by checking properties
                    is_modal = await self._verify_modal_properties(tab, element)
                    if is_modal:
                        return {
                            "detected": True,
                            "type": "modern_facebook_modal",
                            "selector": selector,
                            "element": element,
                            "detection_method": "modern_class_based"
                        }
            except:
                continue
        
        # Strategy 3: JavaScript-based detection
        try:
            js_result = await tab.evaluate("""
                () => {
                    // Look for elements with modal-like properties
                    const candidates = document.querySelectorAll('div');
                    for (let div of candidates) {
                        const style = window.getComputedStyle(div);
                        if (style.position === 'fixed' && 
                            style.zIndex > 1000 &&
                            div.offsetWidth > window.innerWidth * 0.5 &&
                            div.offsetHeight > window.innerHeight * 0.5) {
                            return {
                                detected: true,
                                selector: div.tagName + (div.className ? '.' + div.className.split(' ')[0] : ''),
                                zIndex: style.zIndex,
                                position: style.position
                            };
                        }
                    }
                    return { detected: false };
                }
            """)
            
            if js_result.get("detected"):
                return {
                    "detected": True,
                    "type": "javascript_detected_modal",
                    "selector": js_result["selector"],
                    "element": None,  # Will need to re-select
                    "detection_method": "javascript_analysis",
                    "properties": js_result
                }
        except Exception as e:
            logger.debug(f"JavaScript modal detection failed: {e}")
        
        return {"detected": False}
    
    async def _verify_modal_properties(self, tab, element) -> bool:
        """Verify if an element is actually a modal"""
        try:
            # Check if element has modal-like properties
            properties = await tab.evaluate(f"""
                (element) => {{
                    const style = window.getComputedStyle(element);
                    return {{
                        position: style.position,
                        zIndex: style.zIndex,
                        display: style.display,
                        width: element.offsetWidth,
                        height: element.offsetHeight,
                        hasScrollbar: element.scrollHeight > element.clientHeight
                    }};
                }}
            """, element)
            
            # Modal criteria
            is_fixed_or_absolute = properties.get("position") in ["fixed", "absolute"]
            has_high_z_index = int(properties.get("zIndex", 0)) > 100
            is_visible = properties.get("display") != "none"
            has_reasonable_size = (properties.get("width", 0) > 300 and 
                                 properties.get("height", 0) > 300)
            
            return is_fixed_or_absolute and has_high_z_index and is_visible and has_reasonable_size
            
        except Exception as e:
            logger.debug(f"Error verifying modal properties: {e}")
            return False
    
    async def _analyze_modal_scroll_strategy(self, tab, modal_info) -> Dict[str, Any]:
        """Analyze modal structure to determine optimal scrolling strategy"""
        try:
            modal_element = modal_info.get("element")
            if not modal_element:
                return {"type": "fallback", "reason": "no_modal_element"}
            
            # Check if modal has scrollable content
            scroll_info = await tab.evaluate(f"""
                (modal) => {{
                    return {{
                        hasScrollbar: modal.scrollHeight > modal.clientHeight,
                        scrollHeight: modal.scrollHeight,
                        clientHeight: modal.clientHeight,
                        scrollTop: modal.scrollTop,
                        hasCommentsList: !!modal.querySelector('[data-testid="comments_list"]'),
                        hasScrollableDiv: !!modal.querySelector('div[style*="overflow"]')
                    }};
                }}
            """, modal_element)
            
            if scroll_info.get("hasScrollbar"):
                return {
                    "type": "direct_modal_scroll",
                    "target": "modal_container",
                    "scroll_info": scroll_info,
                    "recommended_distance": min(500, scroll_info.get("clientHeight", 500) // 3)
                }
            elif scroll_info.get("hasCommentsList"):
                return {
                    "type": "content_area_scroll",
                    "target": "comments_list",
                    "scroll_info": scroll_info
                }
            else:
                return {
                    "type": "wheel_event_simulation",
                    "target": "modal_container",
                    "scroll_info": scroll_info
                }
                
        except Exception as e:
            logger.debug(f"Error analyzing modal scroll strategy: {e}")
            return {"type": "fallback", "error": str(e)}
    
    async def _execute_modal_scroll(self, tab, distance: int) -> bool:
        """Execute modal-specific scrolling based on detected strategy"""
        try:
            if not self.detected_modal:
                return False
            
            modal_element = self.detected_modal.get("element")
            scroll_strategy = await self._analyze_modal_scroll_strategy(tab, self.detected_modal)
            
            strategy_type = scroll_strategy.get("type")
            
            if strategy_type == "direct_modal_scroll" and modal_element:
                # Direct scroll on modal element
                await modal_element.scroll_down(distance)
                logger.debug(f"✅ Direct modal scroll: {distance}px")
                return True
                
            elif strategy_type == "content_area_scroll":
                # Scroll within comments list
                success = await self._scroll_comments_area(tab, distance)
                if success:
                    logger.debug(f"✅ Comments area scroll: {distance}px")
                    return True
                    
            elif strategy_type == "wheel_event_simulation":
                # Simulate wheel events
                success = await self._simulate_modal_wheel_scroll(tab, distance)
                if success:
                    logger.debug(f"✅ Wheel event scroll: {distance}px")
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"❌ Error executing modal scroll: {e}")
            return False
    
    async def _scroll_comments_area(self, tab, distance: int) -> bool:
        """Scroll within the comments area of the modal"""
        try:
            comments_selectors = [
                '[data-testid="comments_list"]',
                '[data-pagelet="CommentList"]',
                'div[role="main"]',
                '.x1n2onr6.x1ja2u2z.x78zum5.x2lah0s.xl56j7k'
            ]
            
            for selector in comments_selectors:
                try:
                    comments_area = await tab.select(selector)
                    if comments_area:
                        await comments_area.scroll_down(distance)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Error scrolling comments area: {e}")
            return False
    
    async def _simulate_modal_wheel_scroll(self, tab, distance: int) -> bool:
        """Simulate wheel scroll events within modal"""
        try:
            result = await tab.evaluate(f"""
                (distance) => {{
                    const modal = document.querySelector('div[role="dialog"]') || 
                                 document.querySelector('div[aria-modal="true"]') ||
                                 document.querySelector('.uiLayer');
                    
                    if (modal) {{
                        const wheelEvent = new WheelEvent('wheel', {{
                            deltaY: distance,
                            deltaMode: 0,
                            bubbles: true,
                            cancelable: true
                        }});
                        modal.dispatchEvent(wheelEvent);
                        return true;
                    }}
                    return false;
                }}
            """, distance)
            
            return bool(result)
            
        except Exception as e:
            logger.debug(f"Error simulating wheel scroll: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for modal handling"""
        total_attempts = self.scroll_performance_stats["total_scroll_attempts"]
        success_rate = 0
        if total_attempts > 0:
            successful = self.scroll_performance_stats["successful_modal_scrolls"]
            success_rate = (successful / total_attempts) * 100
        
        return {
            **self.scroll_performance_stats,
            "modal_scroll_success_rate": f"{success_rate:.1f}%",
            "current_modal": self.detected_modal["type"] if self.detected_modal else None
        }


# Factory function
def create_facebook_modal_handler(config: ModalScrollConfig = None) -> FacebookModalHandler:
    """Create a Facebook modal handler with optional configuration"""
    return FacebookModalHandler(config)
