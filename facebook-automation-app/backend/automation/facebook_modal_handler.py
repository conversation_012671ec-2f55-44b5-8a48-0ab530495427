"""
Facebook Modal Handler - Optimized scrolling and interaction for Facebook modals

This module provides specialized handling for Facebook post modals, including:
- Modal detection and identification
- Modal-aware scrolling strategies
- Comment loading optimization
- Cross-browser compatibility
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ModalScrollConfig:
    """Configuration for modal scrolling behavior"""
    scroll_distance: int = 500
    scroll_delay: float = 2.0
    max_scroll_attempts: int = 10
    enable_wheel_events: bool = True
    enable_content_area_detection: bool = True
    enable_adaptive_scrolling: bool = True
    fallback_to_page_scroll: bool = True


class FacebookModalHandler:
    """Handles Facebook modal detection and scrolling optimization"""
    
    def __init__(self, config: ModalScrollConfig = None):
        self.config = config or ModalScrollConfig()
        self.detected_modal = None
        self.scroll_performance_stats = {
            "modal_detections": 0,
            "successful_modal_scrolls": 0,
            "fallback_scrolls": 0,
            "total_scroll_attempts": 0
        }
    
    async def detect_and_configure_modal(self, tab) -> Dict[str, Any]:
        """
        Detect Facebook modal and configure optimal scrolling strategy
        
        Returns:
            Dict with modal info and recommended scrolling approach
        """
        try:
            self.scroll_performance_stats["modal_detections"] += 1
            
            # Step 1: Detect modal container
            modal_info = await self._comprehensive_modal_detection(tab)
            
            if modal_info["detected"]:
                logger.info(f"✅ Modal detected: {modal_info['type']} using {modal_info['selector']}")
                self.detected_modal = modal_info
                
                # Step 2: Analyze modal structure for optimal scrolling
                scroll_strategy = await self._analyze_modal_scroll_strategy(tab, modal_info)
                
                return {
                    "modal_detected": True,
                    "modal_info": modal_info,
                    "scroll_strategy": scroll_strategy,
                    "recommended_approach": "modal_aware_scrolling"
                }
            else:
                logger.info("ℹ️ No modal detected, using standard page scrolling")
                return {
                    "modal_detected": False,
                    "modal_info": None,
                    "scroll_strategy": {"type": "page_scroll"},
                    "recommended_approach": "standard_scrolling"
                }
                
        except Exception as e:
            logger.error(f"❌ Error in modal detection: {e}")
            return {
                "modal_detected": False,
                "error": str(e),
                "recommended_approach": "fallback_scrolling"
            }
    
    async def perform_optimized_scroll(self, tab, scroll_distance: int = None) -> bool:
        """
        Perform optimized scrolling based on detected modal context
        
        Returns:
            bool: True if scroll was successful
        """
        try:
            self.scroll_performance_stats["total_scroll_attempts"] += 1
            distance = scroll_distance or self.config.scroll_distance
            
            if self.detected_modal and self.detected_modal["detected"]:
                # Use modal-aware scrolling
                success = await self._execute_modal_scroll(tab, distance)
                if success:
                    self.scroll_performance_stats["successful_modal_scrolls"] += 1
                    return True
            
            # Fallback to standard scrolling
            if self.config.fallback_to_page_scroll:
                await tab.scroll_down(distance)
                self.scroll_performance_stats["fallback_scrolls"] += 1
                logger.debug(f"Used fallback page scroll: {distance}px")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error in optimized scroll: {e}")
            return False
    
    async def _comprehensive_modal_detection(self, tab) -> Dict[str, Any]:
        """
        Comprehensive Facebook modal detection focused on role="dialog"
        Prioritizes Facebook's standard modal implementation
        """

        # Strategy 1: Primary Facebook modal detection - role="dialog" (HIGHEST PRIORITY)
        try:
            dialog_element = await tab.select('div[role="dialog"]')
            if dialog_element:
                logger.info("✅ Facebook modal detected using role='dialog'")

                # Verify it's a Facebook post modal by checking for comment-related content
                is_post_modal = await self._verify_facebook_post_modal(tab, dialog_element)

                return {
                    "detected": True,
                    "type": "facebook_post_dialog",
                    "selector": 'div[role="dialog"]',
                    "element": dialog_element,
                    "detection_method": "role_dialog_primary",
                    "is_post_modal": is_post_modal,
                    "confidence": "high"
                }
        except Exception as e:
            logger.debug(f"Primary dialog detection failed: {e}")

        # Strategy 2: Fallback modal selectors (for edge cases)
        fallback_selectors = [
            ('div[aria-modal="true"]', 'aria_modal'),
            ('div[data-testid="modal-dialog"]', 'facebook_modal_testid'),
            ('div.uiLayer', 'facebook_ui_layer'),
            ('div._5yd0', 'facebook_legacy_modal')
        ]

        for selector, modal_type in fallback_selectors:
            try:
                element = await tab.select(selector)
                if element:
                    logger.info(f"✅ Fallback modal detected: {modal_type}")
                    return {
                        "detected": True,
                        "type": modal_type,
                        "selector": selector,
                        "element": element,
                        "detection_method": "fallback_selector",
                        "confidence": "medium"
                    }
            except:
                continue

        # Strategy 3: JavaScript-based detection for complex cases
        try:
            js_result = await tab.evaluate("""
                () => {
                    // Look for Facebook modal patterns
                    const dialogElements = document.querySelectorAll('div[role="dialog"]');
                    if (dialogElements.length > 0) {
                        return {
                            detected: true,
                            count: dialogElements.length,
                            method: 'js_role_dialog'
                        };
                    }

                    // Look for elements with modal-like properties
                    const candidates = document.querySelectorAll('div');
                    for (let div of candidates) {
                        const style = window.getComputedStyle(div);
                        if (style.position === 'fixed' &&
                            parseInt(style.zIndex) > 1000 &&
                            div.offsetWidth > window.innerWidth * 0.4 &&
                            div.offsetHeight > window.innerHeight * 0.4) {

                            // Check if it contains comment-related elements
                            const hasComments = div.querySelector('[data-testid*="comment"]') ||
                                              div.querySelector('[aria-label*="comment"]') ||
                                              div.querySelector('div[role="article"]');

                            if (hasComments) {
                                return {
                                    detected: true,
                                    selector: 'div[style*="position: fixed"]',
                                    zIndex: style.zIndex,
                                    position: style.position,
                                    method: 'js_analysis_with_comments'
                                };
                            }
                        }
                    }
                    return { detected: false };
                }
            """)

            if js_result.get("detected"):
                logger.info(f"✅ JavaScript modal detection: {js_result.get('method')}")
                return {
                    "detected": True,
                    "type": "javascript_detected_modal",
                    "selector": js_result.get("selector", "unknown"),
                    "element": None,  # Will need to re-select
                    "detection_method": "javascript_analysis",
                    "properties": js_result,
                    "confidence": "low"
                }
        except Exception as e:
            logger.debug(f"JavaScript modal detection failed: {e}")

        logger.info("ℹ️ No Facebook modal detected")
        return {"detected": False}

    async def _verify_facebook_post_modal(self, tab, dialog_element) -> bool:
        """
        Verify if the detected dialog is actually a Facebook post modal
        by checking for comment-related elements and GraphQL API patterns
        """
        try:
            # Check for Facebook post modal indicators
            modal_indicators = await tab.evaluate(f"""
                (dialogElement) => {{
                    // Check for comment-related elements
                    const commentSelectors = [
                        '[data-testid*="comment"]',
                        '[aria-label*="comment"]',
                        '[aria-label*="Comment"]',
                        'div[role="article"]',
                        '[data-testid="comments_list"]',
                        '[data-pagelet="CommentList"]',
                        'div[data-testid="UFI2Comment/root"]'
                    ];

                    let hasCommentElements = false;
                    for (const selector of commentSelectors) {{
                        if (dialogElement.querySelector(selector)) {{
                            hasCommentElements = true;
                            break;
                        }}
                    }}

                    // Check for Facebook post indicators
                    const postSelectors = [
                        '[data-testid="post_message"]',
                        '[data-testid="story-subtitle"]',
                        '[role="main"]',
                        'div[data-pagelet*="FeedUnit"]'
                    ];

                    let hasPostElements = false;
                    for (const selector of postSelectors) {{
                        if (dialogElement.querySelector(selector)) {{
                            hasPostElements = true;
                            break;
                        }}
                    }}

                    // Check for "View more comments" or similar buttons
                    const viewMoreTexts = [
                        'View more comments',
                        'Xem thêm bình luận',
                        'See more comments',
                        'Load more comments'
                    ];

                    let hasViewMoreButton = false;
                    for (const text of viewMoreTexts) {{
                        if (dialogElement.textContent.includes(text)) {{
                            hasViewMoreButton = true;
                            break;
                        }}
                    }}

                    return {{
                        hasCommentElements,
                        hasPostElements,
                        hasViewMoreButton,
                        modalWidth: dialogElement.offsetWidth,
                        modalHeight: dialogElement.offsetHeight,
                        hasScrollableContent: dialogElement.scrollHeight > dialogElement.clientHeight
                    }};
                }}
            """, dialog_element)

            # Determine if this is a Facebook post modal
            is_post_modal = (
                modal_indicators.get("hasCommentElements", False) or
                modal_indicators.get("hasPostElements", False) or
                modal_indicators.get("hasViewMoreButton", False)
            )

            if is_post_modal:
                logger.info("✅ Confirmed: Facebook post modal with comment functionality")
                logger.debug(f"Modal indicators: {modal_indicators}")
            else:
                logger.info("ℹ️ Dialog detected but not confirmed as post modal")

            return is_post_modal

        except Exception as e:
            logger.debug(f"Error verifying Facebook post modal: {e}")
            return False

    async def _verify_modal_properties(self, tab, element) -> bool:
        """Verify if an element is actually a modal"""
        try:
            # Check if element has modal-like properties
            properties = await tab.evaluate(f"""
                (element) => {{
                    const style = window.getComputedStyle(element);
                    return {{
                        position: style.position,
                        zIndex: style.zIndex,
                        display: style.display,
                        width: element.offsetWidth,
                        height: element.offsetHeight,
                        hasScrollbar: element.scrollHeight > element.clientHeight
                    }};
                }}
            """, element)
            
            # Modal criteria
            is_fixed_or_absolute = properties.get("position") in ["fixed", "absolute"]
            has_high_z_index = int(properties.get("zIndex", 0)) > 100
            is_visible = properties.get("display") != "none"
            has_reasonable_size = (properties.get("width", 0) > 300 and 
                                 properties.get("height", 0) > 300)
            
            return is_fixed_or_absolute and has_high_z_index and is_visible and has_reasonable_size
            
        except Exception as e:
            logger.debug(f"Error verifying modal properties: {e}")
            return False
    
    async def _analyze_modal_scroll_strategy(self, tab, modal_info) -> Dict[str, Any]:
        """Analyze modal structure to determine optimal scrolling strategy"""
        try:
            modal_element = modal_info.get("element")
            if not modal_element:
                return {"type": "fallback", "reason": "no_modal_element"}
            
            # Check if modal has scrollable content
            scroll_info = await tab.evaluate(f"""
                (modal) => {{
                    return {{
                        hasScrollbar: modal.scrollHeight > modal.clientHeight,
                        scrollHeight: modal.scrollHeight,
                        clientHeight: modal.clientHeight,
                        scrollTop: modal.scrollTop,
                        hasCommentsList: !!modal.querySelector('[data-testid="comments_list"]'),
                        hasScrollableDiv: !!modal.querySelector('div[style*="overflow"]')
                    }};
                }}
            """, modal_element)
            
            if scroll_info.get("hasScrollbar"):
                return {
                    "type": "direct_modal_scroll",
                    "target": "modal_container",
                    "scroll_info": scroll_info,
                    "recommended_distance": min(500, scroll_info.get("clientHeight", 500) // 3)
                }
            elif scroll_info.get("hasCommentsList"):
                return {
                    "type": "content_area_scroll",
                    "target": "comments_list",
                    "scroll_info": scroll_info
                }
            else:
                return {
                    "type": "wheel_event_simulation",
                    "target": "modal_container",
                    "scroll_info": scroll_info
                }
                
        except Exception as e:
            logger.debug(f"Error analyzing modal scroll strategy: {e}")
            return {"type": "fallback", "error": str(e)}
    
    async def _execute_modal_scroll(self, tab, distance: int) -> bool:
        """
        Execute Facebook modal-specific scrolling optimized for role="dialog"
        Uses multiple strategies with priority for Facebook post modals
        """
        try:
            if not self.detected_modal:
                return False

            modal_element = self.detected_modal.get("element")
            modal_type = self.detected_modal.get("type", "")

            # Strategy 1: Facebook dialog-specific scrolling (HIGHEST PRIORITY)
            if modal_type == "facebook_post_dialog" and modal_element:
                success = await self._scroll_facebook_dialog(tab, modal_element, distance)
                if success:
                    logger.debug(f"✅ Facebook dialog scroll: {distance}px")
                    return True

            # Strategy 2: Direct modal element scrolling
            if modal_element:
                try:
                    await modal_element.scroll_down(distance)
                    logger.debug(f"✅ Direct modal scroll: {distance}px")
                    return True
                except Exception as e:
                    logger.debug(f"Direct modal scroll failed: {e}")

            # Strategy 3: Comments area scrolling
            success = await self._scroll_comments_area_optimized(tab, distance)
            if success:
                logger.debug(f"✅ Comments area scroll: {distance}px")
                return True

            # Strategy 4: Wheel event simulation for Facebook dialog
            success = await self._simulate_facebook_dialog_scroll(tab, distance)
            if success:
                logger.debug(f"✅ Facebook dialog wheel scroll: {distance}px")
                return True

            return False

        except Exception as e:
            logger.debug(f"❌ Error executing modal scroll: {e}")
            return False

    async def _scroll_facebook_dialog(self, tab, dialog_element, distance: int) -> bool:
        """
        Optimized scrolling specifically for Facebook role="dialog" modals
        """
        try:
            # Method 1: Direct scroll on dialog element
            try:
                await dialog_element.scroll_down(distance)
                logger.debug("✅ Direct Facebook dialog scroll successful")
                return True
            except Exception as e:
                logger.debug(f"Direct dialog scroll failed: {e}")

            # Method 2: Find and scroll the scrollable container within dialog
            scrollable_container = await tab.evaluate(f"""
                (dialogElement) => {{
                    // Look for scrollable containers within the dialog
                    const scrollableSelectors = [
                        'div[data-testid="comments_list"]',
                        'div[role="main"]',
                        'div[data-pagelet="CommentList"]',
                        'div[style*="overflow"]',
                        'div[style*="scroll"]'
                    ];

                    for (const selector of scrollableSelectors) {{
                        const element = dialogElement.querySelector(selector);
                        if (element && element.scrollHeight > element.clientHeight) {{
                            element.scrollTop += {distance};
                            return true;
                        }}
                    }}

                    // Fallback: scroll the dialog itself via JavaScript
                    if (dialogElement.scrollHeight > dialogElement.clientHeight) {{
                        dialogElement.scrollTop += {distance};
                        return true;
                    }}

                    return false;
                }}
            """, dialog_element)

            if scrollable_container:
                logger.debug("✅ Facebook dialog container scroll successful")
                return True

            return False

        except Exception as e:
            logger.debug(f"Error in Facebook dialog scroll: {e}")
            return False

    async def _scroll_comments_area_optimized(self, tab, distance: int) -> bool:
        """
        Optimized scrolling within Facebook comments area
        Prioritizes Facebook-specific comment selectors
        """
        try:
            # Facebook-specific comment area selectors (prioritized)
            facebook_comment_selectors = [
                'div[role="dialog"] [data-testid="comments_list"]',
                'div[role="dialog"] div[role="main"]',
                'div[role="dialog"] [data-pagelet="CommentList"]',
                'div[role="dialog"] div[data-testid="UFI2Comment/root"]',
                '[data-testid="comments_list"]',
                '[data-pagelet="CommentList"]',
                'div[role="main"]'
            ]

            for selector in facebook_comment_selectors:
                try:
                    comments_area = await tab.select(selector)
                    if comments_area:
                        # Check if the area is scrollable
                        is_scrollable = await tab.evaluate(f"""
                            (element) => {{
                                return element.scrollHeight > element.clientHeight;
                            }}
                        """, comments_area)

                        if is_scrollable:
                            await comments_area.scroll_down(distance)
                            logger.debug(f"✅ Comments area scroll with selector: {selector}")
                            return True
                except Exception as e:
                    logger.debug(f"Failed to scroll with selector {selector}: {e}")
                    continue

            return False

        except Exception as e:
            logger.debug(f"Error in optimized comments area scroll: {e}")
            return False

    async def _simulate_facebook_dialog_scroll(self, tab, distance: int) -> bool:
        """
        Simulate wheel scroll events specifically for Facebook dialog modals
        """
        try:
            result = await tab.evaluate(f"""
                (distance) => {{
                    // Find Facebook dialog
                    const dialog = document.querySelector('div[role="dialog"]');
                    if (!dialog) return false;

                    // Create and dispatch wheel event
                    const wheelEvent = new WheelEvent('wheel', {{
                        deltaY: distance,
                        deltaMode: 0,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Try to dispatch on scrollable content within dialog
                    const scrollableContent = dialog.querySelector('[data-testid="comments_list"]') ||
                                            dialog.querySelector('div[role="main"]') ||
                                            dialog.querySelector('[data-pagelet="CommentList"]') ||
                                            dialog;

                    scrollableContent.dispatchEvent(wheelEvent);

                    // Also try scrolling programmatically
                    if (scrollableContent.scrollHeight > scrollableContent.clientHeight) {{
                        scrollableContent.scrollTop += distance;
                        return true;
                    }}

                    return true;
                }}
            """, distance)

            if result:
                logger.debug("✅ Facebook dialog wheel event simulation successful")
                return True

            return False

        except Exception as e:
            logger.debug(f"Error simulating Facebook dialog scroll: {e}")
            return False

    async def _scroll_comments_area(self, tab, distance: int) -> bool:
        """Scroll within the comments area of the modal"""
        try:
            comments_selectors = [
                '[data-testid="comments_list"]',
                '[data-pagelet="CommentList"]',
                'div[role="main"]',
                '.x1n2onr6.x1ja2u2z.x78zum5.x2lah0s.xl56j7k'
            ]
            
            for selector in comments_selectors:
                try:
                    comments_area = await tab.select(selector)
                    if comments_area:
                        await comments_area.scroll_down(distance)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Error scrolling comments area: {e}")
            return False
    
    async def _simulate_modal_wheel_scroll(self, tab, distance: int) -> bool:
        """Simulate wheel scroll events within modal"""
        try:
            result = await tab.evaluate(f"""
                (distance) => {{
                    const modal = document.querySelector('div[role="dialog"]') || 
                                 document.querySelector('div[aria-modal="true"]') ||
                                 document.querySelector('.uiLayer');
                    
                    if (modal) {{
                        const wheelEvent = new WheelEvent('wheel', {{
                            deltaY: distance,
                            deltaMode: 0,
                            bubbles: true,
                            cancelable: true
                        }});
                        modal.dispatchEvent(wheelEvent);
                        return true;
                    }}
                    return false;
                }}
            """, distance)
            
            return bool(result)
            
        except Exception as e:
            logger.debug(f"Error simulating wheel scroll: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for modal handling"""
        total_attempts = self.scroll_performance_stats["total_scroll_attempts"]
        success_rate = 0
        if total_attempts > 0:
            successful = self.scroll_performance_stats["successful_modal_scrolls"]
            success_rate = (successful / total_attempts) * 100
        
        return {
            **self.scroll_performance_stats,
            "modal_scroll_success_rate": f"{success_rate:.1f}%",
            "current_modal": self.detected_modal["type"] if self.detected_modal else None
        }


# Factory function
def create_facebook_modal_handler(config: ModalScrollConfig = None) -> FacebookModalHandler:
    """Create a Facebook modal handler with optional configuration"""
    return FacebookModalHandler(config)
