"""
Test script for Facebook Modal Scrolling functionality

This script demonstrates the optimized modal scrolling approach for Facebook posts
that open in modal dialogs, ensuring proper comment loading and UID extraction.
"""

import asyncio
import sys
import os
import time
from typing import Dict, Any

# Add paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'zendriver_local'))
sys.path.insert(0, os.path.dirname(__file__))

from facebook_modal_handler import create_facebook_modal_handler, ModalScrollConfig
from facebook_comment_interceptor import FacebookCommentAPIInterceptor, InterceptionConfig
import zendriver as zd
from loguru import logger


class FacebookModalScrollingTester:
    """Test class for Facebook modal scrolling functionality"""
    
    def __init__(self):
        self.browser = None
        self.tab = None
        self.modal_handler = None
        self.interceptor = None
    
    async def setup_browser(self, profile_config: Dict[str, Any] = None):
        """Setup browser with antidetect configuration"""
        try:
            logger.info("🚀 Setting up browser for modal scrolling test...")
            
            # Default profile config
            if not profile_config:
                profile_config = {
                    'profile_id': 'test_modal_scroll',
                    'user_data_dir': './browser_profiles/test_modal_scroll',
                    'headless': False,
                    'window_size': (1920, 1080),
                    'position': (-2000, -2000)  # Off-screen positioning
                }
            
            # Browser configuration for visible off-screen
            browser_config = {
                'headless': False,
                'user_data_dir': profile_config.get('user_data_dir', './browser_profiles/test'),
                'window_size': profile_config.get('window_size', (1920, 1080)),
                'disable_blink_features': 'AutomationControlled',
                'exclude_switches': ['enable-automation'],
                'use_subprocess': False,
                'args': [
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    f'--window-position={profile_config.get("position", (-2000, -2000))[0]},{profile_config.get("position", (-2000, -2000))[1]}'
                ]
            }
            
            self.browser = await zd.start(browser_config)
            self.tab = await self.browser.get()
            
            logger.info("✅ Browser setup completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up browser: {e}")
            return False
    
    async def test_modal_detection(self, post_url: str):
        """Test modal detection on a Facebook post"""
        try:
            logger.info(f"🔍 Testing modal detection for: {post_url}")
            
            # Navigate to post
            await self.tab.get(post_url)
            await asyncio.sleep(3)  # Wait for page load
            
            # Create modal handler
            modal_config = ModalScrollConfig(
                scroll_distance=500,
                scroll_delay=2.0,
                max_scroll_attempts=5,
                enable_wheel_events=True,
                enable_content_area_detection=True,
                enable_adaptive_scrolling=True
            )
            
            self.modal_handler = create_facebook_modal_handler(modal_config)
            
            # Test modal detection
            modal_result = await self.modal_handler.detect_and_configure_modal(self.tab)
            
            logger.info("📊 Modal Detection Results:")
            logger.info(f"   Modal Detected: {modal_result['modal_detected']}")
            
            if modal_result['modal_detected']:
                modal_info = modal_result['modal_info']
                logger.info(f"   Modal Type: {modal_info['type']}")
                logger.info(f"   Detection Method: {modal_info['detection_method']}")
                logger.info(f"   Selector Used: {modal_info['selector']}")
                
                scroll_strategy = modal_result['scroll_strategy']
                logger.info(f"   Scroll Strategy: {scroll_strategy['type']}")
                logger.info(f"   Recommended Approach: {modal_result['recommended_approach']}")
            else:
                logger.info("   No modal detected - will use standard page scrolling")
            
            return modal_result
            
        except Exception as e:
            logger.error(f"❌ Error in modal detection test: {e}")
            return None
    
    async def test_modal_scrolling(self, scroll_attempts: int = 5):
        """Test modal scrolling functionality"""
        try:
            logger.info(f"📜 Testing modal scrolling ({scroll_attempts} attempts)...")
            
            if not self.modal_handler:
                logger.error("❌ Modal handler not initialized")
                return False
            
            success_count = 0
            
            for i in range(scroll_attempts):
                logger.info(f"🔄 Scroll attempt {i + 1}/{scroll_attempts}")
                
                # Perform optimized scroll
                scroll_success = await self.modal_handler.perform_optimized_scroll(self.tab, 500)
                
                if scroll_success:
                    success_count += 1
                    logger.info(f"✅ Scroll {i + 1} successful")
                else:
                    logger.warning(f"⚠️ Scroll {i + 1} failed")
                
                # Wait between scrolls
                await asyncio.sleep(2)
            
            # Get performance stats
            stats = self.modal_handler.get_performance_stats()
            logger.info("📈 Scrolling Performance Stats:")
            logger.info(f"   Success Rate: {stats['modal_scroll_success_rate']}")
            logger.info(f"   Total Attempts: {stats['total_scroll_attempts']}")
            logger.info(f"   Successful Modal Scrolls: {stats['successful_modal_scrolls']}")
            logger.info(f"   Fallback Scrolls: {stats['fallback_scrolls']}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ Error in modal scrolling test: {e}")
            return False
    
    async def test_full_comment_extraction(self, post_url: str):
        """Test full comment extraction with modal scrolling"""
        try:
            logger.info(f"💬 Testing full comment extraction for: {post_url}")
            
            # Setup interceptor
            config = InterceptionConfig(
                headless=False,
                off_screen_position=(-2000, -2000),
                window_size=(1920, 1080),
                max_scroll_attempts=5,
                scroll_delay=2.0,
                max_comments=100
            )
            
            profile_config = {
                'profile_id': 'test_extraction',
                'user_data_dir': './browser_profiles/test_extraction'
            }
            
            self.interceptor = FacebookCommentAPIInterceptor(config, profile_config)
            
            # Initialize interceptor
            await self.interceptor.initialize()
            
            # Extract comments
            result = await self.interceptor.extract_comments(post_url)
            
            logger.info("💬 Comment Extraction Results:")
            logger.info(f"   Total Comments: {result.total_extracted}")
            logger.info(f"   Processing Time: {result.processing_time:.2f}s")
            logger.info(f"   API Calls Intercepted: {result.metadata.get('api_calls_intercepted', 0)}")
            logger.info(f"   Scroll Attempts: {result.metadata.get('scroll_attempts', 0)}")
            
            # Show sample comments
            if result.comments:
                logger.info("📝 Sample Comments:")
                for i, comment in enumerate(result.comments[:3]):
                    logger.info(f"   {i+1}. {comment.username}: {comment.comment_text[:50]}...")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error in comment extraction test: {e}")
            return None
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.interceptor:
                await self.interceptor.cleanup()
            
            if self.browser:
                await self.browser.stop()
            
            logger.info("🧹 Cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


async def run_modal_scrolling_tests():
    """Run comprehensive modal scrolling tests"""
    tester = FacebookModalScrollingTester()
    
    try:
        # Test configuration
        test_post_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890"  # Replace with actual post URL
        
        logger.info("🧪 Starting Facebook Modal Scrolling Tests")
        logger.info("=" * 60)
        
        # Step 1: Setup browser
        logger.info("📋 Step 1: Browser Setup")
        setup_success = await tester.setup_browser()
        if not setup_success:
            logger.error("❌ Browser setup failed, aborting tests")
            return
        
        # Step 2: Test modal detection
        logger.info("\n📋 Step 2: Modal Detection Test")
        modal_result = await tester.test_modal_detection(test_post_url)
        if not modal_result:
            logger.error("❌ Modal detection test failed")
            return
        
        # Step 3: Test modal scrolling
        logger.info("\n📋 Step 3: Modal Scrolling Test")
        scroll_success = await tester.test_modal_scrolling(5)
        if not scroll_success:
            logger.warning("⚠️ Modal scrolling test had issues")
        
        # Step 4: Test full comment extraction
        logger.info("\n📋 Step 4: Full Comment Extraction Test")
        extraction_result = await tester.test_full_comment_extraction(test_post_url)
        if extraction_result:
            logger.info("✅ Comment extraction test completed")
        else:
            logger.error("❌ Comment extraction test failed")
        
        logger.info("\n🎉 All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in test execution: {e}")
        
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    # Run the tests
    asyncio.run(run_modal_scrolling_tests())
