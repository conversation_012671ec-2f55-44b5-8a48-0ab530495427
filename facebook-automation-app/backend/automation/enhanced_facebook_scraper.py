"""
Enhanced Facebook Scraper using <PERSON><PERSON> for better network monitoring
and Intersection Observer for smart scrolling
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from playwright.async_api import async_playwright, <PERSON>, Browser, Response

logger = logging.getLogger(__name__)


@dataclass
class FacebookComment:
    uid: str
    username: str
    comment_text: str
    comment_id: str
    created_time: int
    profile_url: Optional[str] = None
    is_anonymous: bool = False
    profile_picture: Optional[str] = None
    gender: Optional[str] = None


@dataclass
class ScrapingConfig:
    headless: bool = False
    off_screen_position: tuple = (-2000, -2000)
    window_size: tuple = (1920, 1080)
    max_comments: int = 1000
    scroll_delay: float = 2.0
    api_timeout: float = 30.0


class EnhancedFacebookScraper:
    """
    Enhanced Facebook scraper using <PERSON>wright for better reliability
    and performance compared to Zendriver + CDP approach
    """
    
    def __init__(self, config: ScrapingConfig = None):
        self.config = config or ScrapingConfig()
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.comments_stream = asyncio.Queue()
        self.api_responses = []
        self.scroll_stats = {
            "total_scrolls": 0,
            "successful_api_calls": 0,
            "comments_extracted": 0,
            "scroll_times": []
        }
        
    async def initialize(self, profile_config: Dict[str, Any] = None) -> bool:
        """Initialize browser with enhanced configuration"""
        try:
            playwright = await async_playwright().start()
            
            # Enhanced browser configuration
            browser_args = [
                f"--window-position={self.config.off_screen_position[0]},{self.config.off_screen_position[1]}",
                f"--window-size={self.config.window_size[0]},{self.config.window_size[1]}",
                "--disable-notifications",
                "--disable-popup-blocking", 
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-first-run",
                "--disable-default-apps",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-web-security",  # For better API access
                "--allow-running-insecure-content"
            ]
            
            # Add user data directory if specified
            launch_options = {
                "headless": self.config.headless,
                "args": browser_args
            }
            
            if profile_config and profile_config.get('user_data_dir'):
                launch_options["user_data_dir"] = profile_config['user_data_dir']
            
            self.browser = await playwright.chromium.launch(**launch_options)
            
            # Create page with enhanced context
            context = await self.browser.new_context(
                viewport={"width": self.config.window_size[0], "height": self.config.window_size[1]},
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
            )
            
            self.page = await context.new_page()
            
            # Setup enhanced network monitoring
            await self._setup_enhanced_network_monitoring()
            
            # Setup smart scrolling with Intersection Observer
            await self._setup_smart_scrolling()
            
            logger.info("✅ Enhanced Facebook scraper initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize enhanced scraper: {e}")
            return False
    
    async def _setup_enhanced_network_monitoring(self):
        """Setup advanced network monitoring with Playwright"""
        try:
            # Intercept GraphQL requests with better error handling
            await self.page.route("**/api/graphql/", self._handle_graphql_request)
            
            # Monitor all responses for better debugging
            self.page.on("response", self._handle_response)
            
            # Monitor console for JavaScript errors
            self.page.on("console", self._handle_console)
            
            logger.info("✅ Enhanced network monitoring setup completed")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup network monitoring: {e}")
            raise
    
    async def _handle_graphql_request(self, route, request):
        """Handle GraphQL requests with enhanced processing"""
        try:
            # Check if this is a comment-related request
            headers = await request.all_headers()
            friendly_name = headers.get('x-fb-friendly-name', '')
            
            comment_api_patterns = [
                'CommentsListComponentsPaginationQuery',
                'CommentListComponentsRootQuery', 
                'UFICommentsProviderPaginationQuery',
                'CometUFICommentsProviderQuery'
            ]
            
            is_comment_request = any(pattern in friendly_name for pattern in comment_api_patterns)
            
            if is_comment_request:
                logger.debug(f"🔍 Intercepting comment API: {friendly_name}")
                
                # Continue request and get response
                response = await route.continue_()
                
                # Process response asynchronously
                asyncio.create_task(self._process_graphql_response(response, friendly_name))
                
            else:
                # Continue non-comment requests normally
                await route.continue_()
                
        except Exception as e:
            logger.warning(f"⚠️ Error handling GraphQL request: {e}")
            await route.continue_()
    
    async def _process_graphql_response(self, response: Response, friendly_name: str):
        """Process GraphQL response and extract comments"""
        try:
            if response.status != 200:
                return
                
            # Get response JSON with timeout
            response_data = await asyncio.wait_for(response.json(), timeout=10.0)
            
            # Extract comments from response
            comments = self._extract_comments_from_graphql(response_data)
            
            if comments:
                logger.info(f"📝 Extracted {len(comments)} comments from {friendly_name}")
                self.scroll_stats["comments_extracted"] += len(comments)
                
                # Stream comments to processing queue
                for comment in comments:
                    await self.comments_stream.put(comment)
                    
                self.scroll_stats["successful_api_calls"] += 1
            
        except asyncio.TimeoutError:
            logger.warning(f"⏰ Timeout processing GraphQL response for {friendly_name}")
        except Exception as e:
            logger.warning(f"⚠️ Error processing GraphQL response: {e}")
    
    async def _handle_response(self, response: Response):
        """Handle all responses for monitoring"""
        if "graphql" in response.url:
            logger.debug(f"📡 GraphQL response: {response.status} - {response.url}")
    
    async def _handle_console(self, msg):
        """Handle console messages for debugging"""
        if msg.type == "error":
            logger.warning(f"🔴 Browser console error: {msg.text}")
    
    async def _setup_smart_scrolling(self):
        """Setup Intersection Observer for smart scrolling"""
        try:
            await self.page.evaluate("""
                // Setup smart scrolling with Intersection Observer
                window.facebookScrollManager = {
                    needsScroll: false,
                    isScrolling: false,
                    lastCommentCount: 0,
                    observer: null,
                    
                    init() {
                        this.setupIntersectionObserver();
                        this.setupMutationObserver();
                    },
                    
                    setupIntersectionObserver() {
                        this.observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting && !this.isScrolling) {
                                    console.log('🔄 Last comment visible, need to scroll');
                                    this.needsScroll = true;
                                }
                            });
                        }, {
                            root: document.querySelector('div[role="dialog"]'),
                            rootMargin: '100px',
                            threshold: 0.1
                        });
                        
                        this.observeLastComment();
                    },
                    
                    setupMutationObserver() {
                        const mutationObserver = new MutationObserver(() => {
                            // Re-observe when new comments are added
                            this.observeLastComment();
                        });
                        
                        const commentsContainer = document.querySelector('div[role="dialog"] [data-testid="comments_list"]');
                        if (commentsContainer) {
                            mutationObserver.observe(commentsContainer, {
                                childList: true,
                                subtree: true
                            });
                        }
                    },
                    
                    observeLastComment() {
                        // Unobserve previous last comment
                        if (this.lastObservedElement) {
                            this.observer.unobserve(this.lastObservedElement);
                        }
                        
                        // Find and observe new last comment
                        const comments = document.querySelectorAll('div[role="dialog"] [data-testid*="comment"]');
                        if (comments.length > 0) {
                            this.lastObservedElement = comments[comments.length - 1];
                            this.observer.observe(this.lastObservedElement);
                            
                            // Check if comment count increased
                            if (comments.length > this.lastCommentCount) {
                                console.log(`📈 Comment count increased: ${this.lastCommentCount} -> ${comments.length}`);
                                this.lastCommentCount = comments.length;
                            }
                        }
                    },
                    
                    async performScroll() {
                        if (this.isScrolling) return false;
                        
                        this.isScrolling = true;
                        this.needsScroll = false;
                        
                        const dialog = document.querySelector('div[role="dialog"]');
                        const commentsContainer = dialog?.querySelector('[data-testid="comments_list"]') || dialog;
                        
                        if (commentsContainer) {
                            // Smooth scroll to trigger API calls
                            commentsContainer.scrollBy({
                                top: 500,
                                behavior: 'smooth'
                            });
                            
                            // Wait for scroll to complete
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                        
                        this.isScrolling = false;
                        return true;
                    }
                };
                
                // Initialize when DOM is ready
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        window.facebookScrollManager.init();
                    });
                } else {
                    window.facebookScrollManager.init();
                }
            """)
            
            logger.info("✅ Smart scrolling with Intersection Observer setup completed")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup smart scrolling: {e}")
            raise
    
    async def scrape_facebook_post(self, post_url: str) -> AsyncGenerator[FacebookComment, None]:
        """
        Scrape Facebook post with enhanced modal handling and smart scrolling
        Returns async generator for real-time comment streaming
        """
        try:
            logger.info(f"🚀 Starting enhanced scraping for: {post_url}")
            
            # Navigate to post
            await self.page.goto(post_url, wait_until="networkidle")
            
            # Wait for modal to appear
            await self.page.wait_for_selector('div[role="dialog"]', timeout=10000)
            logger.info("✅ Facebook modal detected")
            
            # Initialize smart scrolling
            await self.page.evaluate("window.facebookScrollManager.init()")
            
            # Start smart scrolling loop
            scroll_task = asyncio.create_task(self._smart_scroll_loop())
            
            # Stream comments as they are extracted
            comments_yielded = 0
            start_time = time.time()
            
            try:
                while comments_yielded < self.config.max_comments:
                    try:
                        # Get comment from stream with timeout
                        comment = await asyncio.wait_for(
                            self.comments_stream.get(), 
                            timeout=self.config.api_timeout
                        )
                        
                        yield comment
                        comments_yielded += 1
                        
                        logger.debug(f"📤 Yielded comment {comments_yielded}: {comment.username}")
                        
                    except asyncio.TimeoutError:
                        logger.info("⏰ No new comments received, checking if scrolling is complete")
                        
                        # Check if we can still scroll
                        can_scroll = await self.page.evaluate("window.facebookScrollManager.needsScroll")
                        if not can_scroll:
                            logger.info("🏁 No more comments to load, finishing scraping")
                            break
                            
            finally:
                # Cancel scroll task
                scroll_task.cancel()
                
                # Log final statistics
                elapsed_time = time.time() - start_time
                logger.info(f"📊 Scraping completed:")
                logger.info(f"   Comments extracted: {comments_yielded}")
                logger.info(f"   Total scrolls: {self.scroll_stats['total_scrolls']}")
                logger.info(f"   Successful API calls: {self.scroll_stats['successful_api_calls']}")
                logger.info(f"   Time elapsed: {elapsed_time:.2f}s")
                
        except Exception as e:
            logger.error(f"❌ Error in enhanced scraping: {e}")
            raise
    
    async def _smart_scroll_loop(self):
        """Smart scrolling loop using Intersection Observer"""
        try:
            while True:
                # Check if scrolling is needed
                needs_scroll = await self.page.evaluate("window.facebookScrollManager.needsScroll")
                
                if needs_scroll:
                    logger.debug("🔄 Performing smart scroll")
                    
                    scroll_start = time.time()
                    success = await self.page.evaluate("window.facebookScrollManager.performScroll()")
                    scroll_time = time.time() - scroll_start
                    
                    if success:
                        self.scroll_stats["total_scrolls"] += 1
                        self.scroll_stats["scroll_times"].append(scroll_time)
                        
                        # Adaptive delay based on previous scroll performance
                        avg_scroll_time = sum(self.scroll_stats["scroll_times"][-5:]) / min(5, len(self.scroll_stats["scroll_times"]))
                        adaptive_delay = max(avg_scroll_time * 1.5, self.config.scroll_delay)
                        
                        await asyncio.sleep(adaptive_delay)
                    else:
                        await asyncio.sleep(1.0)
                else:
                    await asyncio.sleep(0.5)
                    
        except asyncio.CancelledError:
            logger.info("🛑 Smart scroll loop cancelled")
        except Exception as e:
            logger.error(f"❌ Error in smart scroll loop: {e}")
    
    def _extract_comments_from_graphql(self, response_data: Dict[str, Any]) -> List[FacebookComment]:
        """Extract comments from Facebook GraphQL response"""
        comments = []
        
        try:
            # Navigate through Facebook's GraphQL structure
            if 'data' in response_data and 'node' in response_data['data']:
                node = response_data['data']['node']
                
                if 'comment_rendering_instance_for_feed_location' in node:
                    comment_instance = node['comment_rendering_instance_for_feed_location']
                    
                    if 'comments' in comment_instance and 'edges' in comment_instance['comments']:
                        edges = comment_instance['comments']['edges']
                        
                        for edge in edges:
                            if 'node' in edge:
                                comment = self._parse_comment_node(edge['node'])
                                if comment:
                                    comments.append(comment)
                                    
        except Exception as e:
            logger.warning(f"⚠️ Error extracting comments from GraphQL: {e}")
            
        return comments
    
    def _parse_comment_node(self, comment_node: Dict[str, Any]) -> Optional[FacebookComment]:
        """Parse individual comment node from Facebook GraphQL response"""
        try:
            # Extract basic info
            comment_id = comment_node.get('id', '')
            legacy_fbid = comment_node.get('legacy_fbid', '')
            created_time = comment_node.get('created_time', 0)
            
            # Extract comment text
            comment_text = ''
            for text_source in [('body', 'text'), ('preferred_body', 'text'), ('body_renderer', 'text')]:
                source_key, text_key = text_source
                if source_key in comment_node and text_key in comment_node[source_key]:
                    comment_text = comment_node[source_key][text_key]
                    break
            
            # Extract author info
            author = comment_node.get('author', {})
            uid = author.get('id', '')
            username = author.get('name', 'Unknown')
            is_anonymous = author.get('__typename') == 'GroupAnonAuthorProfile'
            
            # Extract profile info
            profile_url = author.get('url', '')
            if not profile_url and uid:
                if uid.isdigit():
                    profile_url = f"https://www.facebook.com/profile.php?id={uid}"
                else:
                    profile_url = f"https://www.facebook.com/{uid}"
            
            profile_picture = ''
            if 'profile_picture_depth_0' in author and 'uri' in author['profile_picture_depth_0']:
                profile_picture = author['profile_picture_depth_0']['uri']
            
            # Validate required fields
            if not uid or not comment_text:
                return None
            
            return FacebookComment(
                uid=uid,
                username=username,
                comment_text=comment_text,
                comment_id=legacy_fbid or comment_id,
                created_time=created_time,
                profile_url=profile_url,
                is_anonymous=is_anonymous,
                profile_picture=profile_picture
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Error parsing comment node: {e}")
            return None
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            logger.info("🧹 Enhanced scraper cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


# Factory function
async def create_enhanced_facebook_scraper(config: ScrapingConfig = None) -> EnhancedFacebookScraper:
    """Create and initialize enhanced Facebook scraper"""
    scraper = EnhancedFacebookScraper(config)
    success = await scraper.initialize()
    if not success:
        raise Exception("Failed to initialize enhanced Facebook scraper")
    return scraper
