"""
MemoryProfiler - Advanced memory profiling and optimization for hybrid system
"""
import asyncio
import gc
import sys
import time
import tracemalloc
import psutil
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
import json

try:
    import objgraph
    OBJGRAPH_AVAILABLE = True
except ImportError:
    OBJGRAPH_AVAILABLE = False
    logger.warning("objgraph not available - some memory profiling features disabled")


@dataclass
class MemorySnapshot:
    """Memory usage snapshot"""
    timestamp: float
    process_memory_mb: float
    process_memory_percent: float
    system_memory_mb: float
    system_memory_percent: float
    gc_objects: int
    gc_collections: Dict[str, int]
    top_objects: List[Tuple[str, int]] = None


class MemoryProfiler:
    """Advanced memory profiler for performance optimization"""
    
    def __init__(self):
        self.profiling_active = False
        self.snapshots: List[MemorySnapshot] = []
        self.baseline_snapshot: Optional[MemorySnapshot] = None
        self.tracemalloc_started = False
        
        # Memory thresholds
        self.memory_warning_threshold_mb = 1000  # 1GB
        self.memory_critical_threshold_mb = 2000  # 2GB
        self.memory_leak_threshold_mb = 100  # 100MB growth
        
        # Monitoring settings
        self.snapshot_interval = 5.0  # seconds
        self.max_snapshots = 1000
        
    async def start_profiling(self) -> bool:
        """Start memory profiling"""
        try:
            # Start tracemalloc for detailed memory tracking
            if not self.tracemalloc_started:
                tracemalloc.start()
                self.tracemalloc_started = True
            
            # Take baseline snapshot
            self.baseline_snapshot = self._take_snapshot()
            self.snapshots = [self.baseline_snapshot]
            
            self.profiling_active = True
            
            # Start background monitoring
            asyncio.create_task(self._monitoring_loop())
            
            logger.info("Memory profiling started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start memory profiling: {e}")
            return False
    
    async def stop_profiling(self) -> Dict[str, Any]:
        """Stop profiling and return analysis"""
        try:
            self.profiling_active = False
            
            # Take final snapshot
            final_snapshot = self._take_snapshot()
            self.snapshots.append(final_snapshot)
            
            # Analyze memory usage
            analysis = self._analyze_memory_usage()
            
            # Stop tracemalloc
            if self.tracemalloc_started:
                tracemalloc.stop()
                self.tracemalloc_started = False
            
            logger.info("Memory profiling stopped")
            return analysis
            
        except Exception as e:
            logger.error(f"Error stopping memory profiling: {e}")
            return {"error": str(e)}
    
    def _take_snapshot(self) -> MemorySnapshot:
        """Take memory usage snapshot"""
        try:
            process = psutil.Process()
            
            # Process memory
            memory_info = process.memory_info()
            process_memory_mb = memory_info.rss / 1024 / 1024
            process_memory_percent = process.memory_percent()
            
            # System memory
            system_memory = psutil.virtual_memory()
            system_memory_mb = system_memory.used / 1024 / 1024
            system_memory_percent = system_memory.percent
            
            # Garbage collection info
            gc_objects = len(gc.get_objects())
            gc_collections = {
                f"generation_{i}": gc.get_count()[i] for i in range(3)
            }
            
            # Top objects (if objgraph available)
            top_objects = None
            if OBJGRAPH_AVAILABLE:
                try:
                    top_objects = objgraph.most_common_types(limit=10)
                except Exception:
                    pass
            
            return MemorySnapshot(
                timestamp=time.time(),
                process_memory_mb=process_memory_mb,
                process_memory_percent=process_memory_percent,
                system_memory_mb=system_memory_mb,
                system_memory_percent=system_memory_percent,
                gc_objects=gc_objects,
                gc_collections=gc_collections,
                top_objects=top_objects
            )
            
        except Exception as e:
            logger.error(f"Error taking memory snapshot: {e}")
            return MemorySnapshot(
                timestamp=time.time(),
                process_memory_mb=0,
                process_memory_percent=0,
                system_memory_mb=0,
                system_memory_percent=0,
                gc_objects=0,
                gc_collections={}
            )
    
    async def _monitoring_loop(self):
        """Background memory monitoring loop"""
        while self.profiling_active:
            try:
                # Take snapshot
                snapshot = self._take_snapshot()
                self.snapshots.append(snapshot)
                
                # Limit snapshot history
                if len(self.snapshots) > self.max_snapshots:
                    self.snapshots = self.snapshots[-self.max_snapshots:]
                
                # Check for memory issues
                await self._check_memory_issues(snapshot)
                
                await asyncio.sleep(self.snapshot_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {e}")
                await asyncio.sleep(self.snapshot_interval)
    
    async def _check_memory_issues(self, snapshot: MemorySnapshot):
        """Check for memory issues and warnings"""
        try:
            # Check memory thresholds
            if snapshot.process_memory_mb > self.memory_critical_threshold_mb:
                logger.critical(f"Critical memory usage: {snapshot.process_memory_mb:.1f}MB")
                await self._trigger_emergency_cleanup()
            elif snapshot.process_memory_mb > self.memory_warning_threshold_mb:
                logger.warning(f"High memory usage: {snapshot.process_memory_mb:.1f}MB")
            
            # Check for memory leaks
            if self.baseline_snapshot and len(self.snapshots) > 10:
                memory_growth = snapshot.process_memory_mb - self.baseline_snapshot.process_memory_mb
                if memory_growth > self.memory_leak_threshold_mb:
                    logger.warning(f"Potential memory leak detected: {memory_growth:.1f}MB growth")
            
        except Exception as e:
            logger.error(f"Error checking memory issues: {e}")
    
    async def _trigger_emergency_cleanup(self):
        """Trigger emergency memory cleanup"""
        try:
            logger.info("Triggering emergency memory cleanup...")
            
            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
            
            # Clear caches if available
            # This would be implemented based on specific cache systems used
            
        except Exception as e:
            logger.error(f"Error in emergency cleanup: {e}")
    
    def _analyze_memory_usage(self) -> Dict[str, Any]:
        """Analyze memory usage patterns"""
        try:
            if len(self.snapshots) < 2:
                return {"error": "Insufficient data for analysis"}
            
            # Calculate memory trends
            memory_values = [s.process_memory_mb for s in self.snapshots]
            time_values = [s.timestamp for s in self.snapshots]
            
            # Memory statistics
            memory_stats = {
                "baseline_mb": self.baseline_snapshot.process_memory_mb if self.baseline_snapshot else 0,
                "current_mb": memory_values[-1],
                "peak_mb": max(memory_values),
                "min_mb": min(memory_values),
                "avg_mb": sum(memory_values) / len(memory_values),
                "growth_mb": memory_values[-1] - memory_values[0],
                "growth_rate_mb_per_hour": self._calculate_growth_rate(memory_values, time_values)
            }
            
            # Object count analysis
            object_counts = [s.gc_objects for s in self.snapshots]
            object_stats = {
                "baseline_objects": object_counts[0],
                "current_objects": object_counts[-1],
                "peak_objects": max(object_counts),
                "object_growth": object_counts[-1] - object_counts[0]
            }
            
            # Memory leak detection
            leak_analysis = self._detect_memory_leaks()
            
            # Performance impact
            performance_impact = self._analyze_performance_impact()
            
            # Optimization recommendations
            recommendations = self._generate_memory_recommendations(memory_stats, object_stats, leak_analysis)
            
            return {
                "profiling_duration": time_values[-1] - time_values[0],
                "snapshots_taken": len(self.snapshots),
                "memory_statistics": memory_stats,
                "object_statistics": object_stats,
                "leak_analysis": leak_analysis,
                "performance_impact": performance_impact,
                "recommendations": recommendations,
                "detailed_snapshots": self.snapshots[-10:]  # Last 10 snapshots
            }
            
        except Exception as e:
            logger.error(f"Error analyzing memory usage: {e}")
            return {"error": str(e)}
    
    def _calculate_growth_rate(self, memory_values: List[float], time_values: List[float]) -> float:
        """Calculate memory growth rate per hour"""
        try:
            if len(memory_values) < 2:
                return 0.0
            
            time_diff_hours = (time_values[-1] - time_values[0]) / 3600
            memory_diff = memory_values[-1] - memory_values[0]
            
            return memory_diff / time_diff_hours if time_diff_hours > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def _detect_memory_leaks(self) -> Dict[str, Any]:
        """Detect potential memory leaks"""
        try:
            if len(self.snapshots) < 10:
                return {"status": "insufficient_data"}
            
            # Analyze memory trend over time
            recent_snapshots = self.snapshots[-10:]
            memory_values = [s.process_memory_mb for s in recent_snapshots]
            
            # Check for consistent growth
            growth_points = 0
            for i in range(1, len(memory_values)):
                if memory_values[i] > memory_values[i-1]:
                    growth_points += 1
            
            growth_ratio = growth_points / (len(memory_values) - 1)
            
            # Determine leak probability
            if growth_ratio > 0.8:
                leak_probability = "high"
            elif growth_ratio > 0.6:
                leak_probability = "medium"
            elif growth_ratio > 0.4:
                leak_probability = "low"
            else:
                leak_probability = "none"
            
            return {
                "status": "analyzed",
                "leak_probability": leak_probability,
                "growth_ratio": growth_ratio,
                "consistent_growth_points": growth_points,
                "total_growth_mb": memory_values[-1] - memory_values[0]
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def _analyze_performance_impact(self) -> Dict[str, Any]:
        """Analyze performance impact of memory usage"""
        try:
            if not self.snapshots:
                return {"status": "no_data"}
            
            current_snapshot = self.snapshots[-1]
            
            # Determine performance impact level
            memory_mb = current_snapshot.process_memory_mb
            
            if memory_mb > 2000:
                impact_level = "critical"
                impact_description = "Very high memory usage may cause system instability"
            elif memory_mb > 1000:
                impact_level = "high"
                impact_description = "High memory usage may affect performance"
            elif memory_mb > 500:
                impact_level = "medium"
                impact_description = "Moderate memory usage, monitor for growth"
            else:
                impact_level = "low"
                impact_description = "Memory usage within acceptable limits"
            
            # GC pressure analysis
            gc_pressure = "low"
            if len(self.snapshots) > 5:
                recent_snapshots = self.snapshots[-5:]
                object_counts = [s.gc_objects for s in recent_snapshots]
                if max(object_counts) - min(object_counts) > 10000:
                    gc_pressure = "high"
                elif max(object_counts) - min(object_counts) > 5000:
                    gc_pressure = "medium"
            
            return {
                "status": "analyzed",
                "impact_level": impact_level,
                "impact_description": impact_description,
                "current_memory_mb": memory_mb,
                "gc_pressure": gc_pressure,
                "system_memory_percent": current_snapshot.system_memory_percent
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def _generate_memory_recommendations(
        self,
        memory_stats: Dict[str, Any],
        object_stats: Dict[str, Any],
        leak_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate memory optimization recommendations"""
        recommendations = []
        
        # Memory usage recommendations
        current_mb = memory_stats.get("current_mb", 0)
        growth_mb = memory_stats.get("growth_mb", 0)
        
        if current_mb > 1500:
            recommendations.append("Critical: Reduce memory usage immediately - consider restarting services")
        elif current_mb > 1000:
            recommendations.append("High memory usage detected - implement memory optimization strategies")
        
        if growth_mb > 200:
            recommendations.append("Significant memory growth detected - investigate potential memory leaks")
        
        # Object count recommendations
        object_growth = object_stats.get("object_growth", 0)
        if object_growth > 50000:
            recommendations.append("High object count growth - review object lifecycle management")
        
        # Leak-specific recommendations
        leak_probability = leak_analysis.get("leak_probability", "none")
        if leak_probability == "high":
            recommendations.append("High probability memory leak - immediate investigation required")
            recommendations.append("Use memory profiling tools to identify leak sources")
        elif leak_probability == "medium":
            recommendations.append("Potential memory leak detected - monitor closely")
        
        # General optimization recommendations
        recommendations.extend([
            "Implement regular garbage collection cycles",
            "Use object pooling for frequently created objects",
            "Clear caches periodically",
            "Monitor shared memory usage",
            "Consider memory-efficient data structures"
        ])
        
        # Performance-specific recommendations
        growth_rate = memory_stats.get("growth_rate_mb_per_hour", 0)
        if growth_rate > 100:
            recommendations.append("High memory growth rate - implement memory usage limits")
        
        return recommendations
    
    async def generate_memory_report(self) -> str:
        """Generate detailed memory profiling report"""
        try:
            analysis = self._analyze_memory_usage()
            
            report = []
            report.append("="*80)
            report.append("MEMORY PROFILING REPORT")
            report.append("="*80)
            
            # Summary
            memory_stats = analysis.get("memory_statistics", {})
            report.append(f"\n📊 MEMORY SUMMARY")
            report.append("-" * 40)
            report.append(f"Profiling Duration: {analysis.get('profiling_duration', 0):.1f} seconds")
            report.append(f"Snapshots Taken: {analysis.get('snapshots_taken', 0)}")
            report.append(f"Current Memory: {memory_stats.get('current_mb', 0):.1f} MB")
            report.append(f"Peak Memory: {memory_stats.get('peak_mb', 0):.1f} MB")
            report.append(f"Memory Growth: {memory_stats.get('growth_mb', 0):.1f} MB")
            report.append(f"Growth Rate: {memory_stats.get('growth_rate_mb_per_hour', 0):.1f} MB/hour")
            
            # Leak analysis
            leak_analysis = analysis.get("leak_analysis", {})
            report.append(f"\n🔍 LEAK ANALYSIS")
            report.append("-" * 40)
            report.append(f"Leak Probability: {leak_analysis.get('leak_probability', 'unknown').upper()}")
            report.append(f"Growth Ratio: {leak_analysis.get('growth_ratio', 0):.2f}")
            
            # Performance impact
            performance = analysis.get("performance_impact", {})
            report.append(f"\n⚡ PERFORMANCE IMPACT")
            report.append("-" * 40)
            report.append(f"Impact Level: {performance.get('impact_level', 'unknown').upper()}")
            report.append(f"Description: {performance.get('impact_description', 'N/A')}")
            report.append(f"GC Pressure: {performance.get('gc_pressure', 'unknown').upper()}")
            
            # Recommendations
            recommendations = analysis.get("recommendations", [])
            report.append(f"\n💡 RECOMMENDATIONS")
            report.append("-" * 40)
            for i, rec in enumerate(recommendations[:10], 1):
                report.append(f"{i:2d}. {rec}")
            
            report.append("\n" + "="*80)
            
            return "\n".join(report)
            
        except Exception as e:
            return f"Error generating memory report: {e}"


async def main():
    """Main memory profiling function"""
    try:
        logger.info("Starting Memory Profiling Demo")
        
        profiler = MemoryProfiler()
        
        # Start profiling
        await profiler.start_profiling()
        
        # Simulate some memory usage
        logger.info("Simulating memory usage...")
        data = []
        for i in range(1000):
            data.append([j for j in range(1000)])  # Create some objects
            if i % 100 == 0:
                await asyncio.sleep(1)
        
        # Wait for monitoring
        await asyncio.sleep(10)
        
        # Stop profiling and get results
        analysis = await profiler.stop_profiling()
        
        # Generate and print report
        report = await profiler.generate_memory_report()
        print(report)
        
        # Save detailed analysis
        with open("memory_analysis.json", "w") as f:
            json.dump(analysis, f, indent=2, default=str)
        
        logger.info("Memory profiling completed. Analysis saved to memory_analysis.json")
        
    except Exception as e:
        logger.error(f"Memory profiling failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run memory profiling
    asyncio.run(main())
