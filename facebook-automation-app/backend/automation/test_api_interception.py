"""
Test script for Facebook API Interception approach
Tests the new implementation with real Facebook posts
"""
import asyncio
import time
import json
from typing import Dict, Any
from loguru import logger
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from facebook_scraper_service import create_facebook_scraper_service
from facebook_comment_interceptor import create_facebook_comment_interceptor
from stealth_browser_manager import create_facebook_profile, ProxyConfig


class FacebookAPIInterceptionTester:
    """Test suite for Facebook API interception"""
    
    def __init__(self):
        self.test_results = []
        self.scraper_service = None
        
    async def setup(self):
        """Setup test environment"""
        try:
            # Create scraper service
            self.scraper_service = await create_facebook_scraper_service("test_data")
            logger.info("Test environment setup completed")
            return True
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            return False
    
    async def test_basic_comment_extraction(self):
        """Test basic comment extraction from Facebook post"""
        test_name = "Basic Comment Extraction"
        logger.info(f"Starting test: {test_name}")
        
        start_time = time.time()
        
        try:
            # Test configuration
            profile_config = {
                'profile_id': 'test_profile_001',
                'profile_name': 'API Interception Test',
                'user_data_dir': './test_browser_profiles/test_001',
                'proxy': None  # No proxy for testing
            }
            
            # Test with a Facebook post (you should replace with actual test post)
            test_post_url = "https://www.facebook.com/groups/example/posts/123456789/"
            
            # Run extraction
            result = await self.scraper_service.scrape_facebook_post_uids(
                profile_config=profile_config,
                post_url=test_post_url,
                max_comments=100
            )
            
            test_time = time.time() - start_time
            
            # Evaluate results
            success = result.get("success", False)
            if success:
                results = result["results"]
                uids_found = results.get("total_uids_found", 0)
                comments_extracted = results.get("total_comments_extracted", 0)
                api_calls = results.get("api_interception_stats", {}).get("api_calls_intercepted", 0)
                
                test_result = {
                    "test_name": test_name,
                    "success": True,
                    "execution_time": test_time,
                    "metrics": {
                        "uids_found": uids_found,
                        "comments_extracted": comments_extracted,
                        "api_calls_intercepted": api_calls,
                        "extraction_method": results.get("extraction_method", "unknown")
                    },
                    "performance": {
                        "uids_per_second": uids_found / test_time if test_time > 0 else 0,
                        "comments_per_second": comments_extracted / test_time if test_time > 0 else 0
                    }
                }
                
                logger.info(f"✅ {test_name} PASSED: {uids_found} UIDs from {comments_extracted} comments in {test_time:.2f}s")
            else:
                test_result = {
                    "test_name": test_name,
                    "success": False,
                    "execution_time": test_time,
                    "error": result.get("error", "Unknown error")
                }
                logger.error(f"❌ {test_name} FAILED: {result.get('error', 'Unknown error')}")
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_time = time.time() - start_time
            test_result = {
                "test_name": test_name,
                "success": False,
                "execution_time": test_time,
                "error": str(e)
            }
            self.test_results.append(test_result)
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            return test_result
    
    async def test_interceptor_direct(self):
        """Test interceptor directly without scraper service"""
        test_name = "Direct Interceptor Test"
        logger.info(f"Starting test: {test_name}")
        
        start_time = time.time()
        interceptor = None
        
        try:
            # Create profile configuration
            profile_config = {
                'user_data_dir': './test_browser_profiles/direct_test',
                'proxy': None
            }
            
            # Create interceptor directly
            interceptor = await create_facebook_comment_interceptor(
                profile_config=profile_config,
                max_comments=50,
                headless=False
            )
            
            # Test with Facebook post
            test_post_url = "https://www.facebook.com/groups/example/posts/123456789/"
            
            # Extract comments
            extraction_result = await interceptor.extract_comments(test_post_url)
            
            test_time = time.time() - start_time
            
            # Evaluate results
            comments_count = extraction_result.total_extracted
            processing_time = extraction_result.processing_time
            api_calls = extraction_result.metadata.get('api_calls_intercepted', 0)
            
            test_result = {
                "test_name": test_name,
                "success": True,
                "execution_time": test_time,
                "metrics": {
                    "comments_extracted": comments_count,
                    "api_calls_intercepted": api_calls,
                    "processing_time": processing_time
                },
                "performance": {
                    "comments_per_second": comments_count / processing_time if processing_time > 0 else 0,
                    "api_efficiency": comments_count / api_calls if api_calls > 0 else 0
                }
            }
            
            logger.info(f"✅ {test_name} PASSED: {comments_count} comments via {api_calls} API calls in {test_time:.2f}s")
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_time = time.time() - start_time
            test_result = {
                "test_name": test_name,
                "success": False,
                "execution_time": test_time,
                "error": str(e)
            }
            self.test_results.append(test_result)
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            return test_result
        
        finally:
            if interceptor:
                try:
                    await interceptor.cleanup()
                except Exception as e:
                    logger.warning(f"Error cleaning up interceptor: {e}")
    
    async def test_performance_benchmark(self):
        """Performance benchmark test"""
        test_name = "Performance Benchmark"
        logger.info(f"Starting test: {test_name}")
        
        start_time = time.time()
        
        try:
            profile_config = {
                'profile_id': 'benchmark_profile',
                'profile_name': 'Performance Benchmark',
                'user_data_dir': './test_browser_profiles/benchmark',
                'proxy': None
            }
            
            # Test with larger comment limit
            test_post_url = "https://www.facebook.com/groups/example/posts/123456789/"
            
            result = await self.scraper_service.scrape_facebook_post_uids(
                profile_config=profile_config,
                post_url=test_post_url,
                max_comments=500  # Higher limit for performance test
            )
            
            test_time = time.time() - start_time
            
            if result.get("success", False):
                results = result["results"]
                
                # Performance metrics
                uids_found = results.get("total_uids_found", 0)
                comments_extracted = results.get("total_comments_extracted", 0)
                scraping_time = results.get("scraping_time", 0)
                api_stats = results.get("api_interception_stats", {})
                
                test_result = {
                    "test_name": test_name,
                    "success": True,
                    "execution_time": test_time,
                    "performance_metrics": {
                        "total_uids": uids_found,
                        "total_comments": comments_extracted,
                        "scraping_time": scraping_time,
                        "api_calls_intercepted": api_stats.get("api_calls_intercepted", 0),
                        "scroll_attempts": api_stats.get("scroll_attempts", 0),
                        "uids_per_second": uids_found / scraping_time if scraping_time > 0 else 0,
                        "comments_per_second": comments_extracted / scraping_time if scraping_time > 0 else 0,
                        "api_efficiency": comments_extracted / api_stats.get("api_calls_intercepted", 1)
                    }
                }
                
                logger.info(f"✅ {test_name} PASSED: {uids_found} UIDs, {comments_extracted} comments in {scraping_time:.2f}s")
            else:
                test_result = {
                    "test_name": test_name,
                    "success": False,
                    "execution_time": test_time,
                    "error": result.get("error", "Unknown error")
                }
                logger.error(f"❌ {test_name} FAILED: {result.get('error', 'Unknown error')}")
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_time = time.time() - start_time
            test_result = {
                "test_name": test_name,
                "success": False,
                "execution_time": test_time,
                "error": str(e)
            }
            self.test_results.append(test_result)
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            return test_result
    
    async def test_error_handling(self):
        """Test error handling with invalid URLs"""
        test_name = "Error Handling Test"
        logger.info(f"Starting test: {test_name}")
        
        start_time = time.time()
        
        try:
            profile_config = {
                'profile_id': 'error_test_profile',
                'profile_name': 'Error Handling Test',
                'user_data_dir': './test_browser_profiles/error_test',
                'proxy': None
            }
            
            # Test with invalid URL
            invalid_url = "https://www.facebook.com/invalid/post/url"
            
            result = await self.scraper_service.scrape_facebook_post_uids(
                profile_config=profile_config,
                post_url=invalid_url,
                max_comments=10
            )
            
            test_time = time.time() - start_time
            
            # For error handling test, we expect it to handle errors gracefully
            test_result = {
                "test_name": test_name,
                "success": True,  # Success means it handled the error gracefully
                "execution_time": test_time,
                "error_handling": {
                    "handled_gracefully": not result.get("success", True),
                    "error_message": result.get("error", "No error"),
                    "returned_valid_response": isinstance(result, dict)
                }
            }
            
            logger.info(f"✅ {test_name} PASSED: Error handled gracefully")
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_time = time.time() - start_time
            test_result = {
                "test_name": test_name,
                "success": False,
                "execution_time": test_time,
                "error": str(e)
            }
            self.test_results.append(test_result)
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            return test_result
    
    async def run_all_tests(self):
        """Run all tests"""
        logger.info("🚀 Starting Facebook API Interception Test Suite")
        
        if not await self.setup():
            logger.error("❌ Test setup failed, aborting")
            return
        
        tests = [
            self.test_basic_comment_extraction,
            self.test_interceptor_direct,
            self.test_performance_benchmark,
            self.test_error_handling
        ]
        
        for test_func in tests:
            try:
                await test_func()
                await asyncio.sleep(2)  # Brief pause between tests
            except Exception as e:
                logger.error(f"Test {test_func.__name__} failed with exception: {e}")
        
        # Generate test report
        await self.generate_test_report()
        
        # Cleanup
        if self.scraper_service:
            await self.scraper_service.cleanup()
    
    async def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("📊 Generating test report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "test_results": self.test_results,
            "timestamp": time.time()
        }
        
        # Save report to file
        report_file = f"test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        logger.info(f"📋 Test Summary: {passed_tests}/{total_tests} tests passed ({report['test_summary']['success_rate']:.1f}%)")
        logger.info(f"📄 Detailed report saved to: {report_file}")
        
        return report


async def main():
    """Main test function"""
    tester = FacebookAPIInterceptionTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # Run tests
    asyncio.run(main())
