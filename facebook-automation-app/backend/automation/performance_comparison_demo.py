"""
Performance Comparison Demo: Current vs Enhanced Facebook Scraping
Compares Zendriver+CDP vs Playwright+IntersectionObserver approaches
"""

import asyncio
import time
import logging
from typing import Dict, List, Any
from dataclasses import dataclass

# Import both implementations
from facebook_comment_interceptor import <PERSON><PERSON>omment<PERSON><PERSON>nterceptor, InterceptionConfig
from enhanced_facebook_scraper import EnhancedFacebookScraper, ScrapingConfig

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    scraper_type: str
    total_time: float
    comments_extracted: int
    scroll_attempts: int
    api_calls_successful: int
    memory_usage_mb: float
    error_count: int
    success_rate: float


class PerformanceComparison:
    """Compare performance between current and enhanced scrapers"""
    
    def __init__(self):
        self.results = []
        
    async def run_comparison(self, test_post_url: str, max_comments: int = 100):
        """Run performance comparison between both scrapers"""
        
        logger.info("🏁 Starting Performance Comparison")
        logger.info("=" * 60)
        
        # Test 1: Current Zendriver + CDP approach
        logger.info("📋 Test 1: Current Zendriver + CDP Approach")
        current_metrics = await self._test_current_scraper(test_post_url, max_comments)
        
        # Wait between tests
        await asyncio.sleep(5)
        
        # Test 2: Enhanced Playwright + Intersection Observer approach  
        logger.info("\n📋 Test 2: Enhanced Playwright + Intersection Observer Approach")
        enhanced_metrics = await self._test_enhanced_scraper(test_post_url, max_comments)
        
        # Compare results
        self._compare_results(current_metrics, enhanced_metrics)
        
        return {
            "current": current_metrics,
            "enhanced": enhanced_metrics
        }
    
    async def _test_current_scraper(self, post_url: str, max_comments: int) -> PerformanceMetrics:
        """Test current Zendriver + CDP scraper"""
        start_time = time.time()
        comments_extracted = 0
        error_count = 0
        
        try:
            # Setup current scraper
            config = InterceptionConfig(
                headless=False,
                off_screen_position=(-2000, -2000),
                max_comments=max_comments,
                scroll_delay=2.0,
                max_scroll_attempts=10
            )
            
            profile_config = {
                'profile_id': 'test_current',
                'user_data_dir': './browser_profiles/test_current'
            }
            
            interceptor = FacebookCommentAPIInterceptor(config, profile_config)
            
            # Initialize and run
            await interceptor.initialize()
            
            logger.info("🚀 Starting current scraper...")
            result = await interceptor.extract_comments(post_url)
            
            comments_extracted = result.total_extracted
            scroll_attempts = result.metadata.get('scroll_attempts', 0)
            api_calls = result.metadata.get('api_calls_intercepted', 0)
            
            # Cleanup
            await interceptor.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Error in current scraper: {e}")
            error_count += 1
            scroll_attempts = 0
            api_calls = 0
        
        total_time = time.time() - start_time
        success_rate = (comments_extracted / max_comments) * 100 if max_comments > 0 else 0
        
        metrics = PerformanceMetrics(
            scraper_type="Current (Zendriver + CDP)",
            total_time=total_time,
            comments_extracted=comments_extracted,
            scroll_attempts=scroll_attempts,
            api_calls_successful=api_calls,
            memory_usage_mb=0,  # Would need psutil to measure
            error_count=error_count,
            success_rate=success_rate
        )
        
        logger.info(f"📊 Current scraper results:")
        logger.info(f"   Time: {total_time:.2f}s")
        logger.info(f"   Comments: {comments_extracted}")
        logger.info(f"   Scrolls: {scroll_attempts}")
        logger.info(f"   API calls: {api_calls}")
        logger.info(f"   Success rate: {success_rate:.1f}%")
        
        return metrics
    
    async def _test_enhanced_scraper(self, post_url: str, max_comments: int) -> PerformanceMetrics:
        """Test enhanced Playwright + Intersection Observer scraper"""
        start_time = time.time()
        comments_extracted = 0
        error_count = 0
        
        try:
            # Setup enhanced scraper
            config = ScrapingConfig(
                headless=False,
                off_screen_position=(-2000, -2000),
                max_comments=max_comments,
                scroll_delay=1.5,  # Faster due to smart scrolling
                api_timeout=30.0
            )
            
            profile_config = {
                'user_data_dir': './browser_profiles/test_enhanced'
            }
            
            scraper = EnhancedFacebookScraper(config)
            await scraper.initialize(profile_config)
            
            logger.info("🚀 Starting enhanced scraper...")
            
            # Collect comments from async generator
            async for comment in scraper.scrape_facebook_post(post_url):
                comments_extracted += 1
                logger.debug(f"📝 Received comment {comments_extracted}: {comment.username}")
                
                if comments_extracted >= max_comments:
                    break
            
            scroll_attempts = scraper.scroll_stats["total_scrolls"]
            api_calls = scraper.scroll_stats["successful_api_calls"]
            
            # Cleanup
            await scraper.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Error in enhanced scraper: {e}")
            error_count += 1
            scroll_attempts = 0
            api_calls = 0
        
        total_time = time.time() - start_time
        success_rate = (comments_extracted / max_comments) * 100 if max_comments > 0 else 0
        
        metrics = PerformanceMetrics(
            scraper_type="Enhanced (Playwright + Intersection Observer)",
            total_time=total_time,
            comments_extracted=comments_extracted,
            scroll_attempts=scroll_attempts,
            api_calls_successful=api_calls,
            memory_usage_mb=0,  # Would need psutil to measure
            error_count=error_count,
            success_rate=success_rate
        )
        
        logger.info(f"📊 Enhanced scraper results:")
        logger.info(f"   Time: {total_time:.2f}s")
        logger.info(f"   Comments: {comments_extracted}")
        logger.info(f"   Scrolls: {scroll_attempts}")
        logger.info(f"   API calls: {api_calls}")
        logger.info(f"   Success rate: {success_rate:.1f}%")
        
        return metrics
    
    def _compare_results(self, current: PerformanceMetrics, enhanced: PerformanceMetrics):
        """Compare and analyze results"""
        
        logger.info("\n🔍 Performance Comparison Analysis")
        logger.info("=" * 60)
        
        # Time comparison
        time_improvement = ((current.total_time - enhanced.total_time) / current.total_time) * 100
        logger.info(f"⏱️  Time Performance:")
        logger.info(f"   Current: {current.total_time:.2f}s")
        logger.info(f"   Enhanced: {enhanced.total_time:.2f}s")
        logger.info(f"   Improvement: {time_improvement:+.1f}%")
        
        # Comments extraction comparison
        comments_improvement = enhanced.comments_extracted - current.comments_extracted
        logger.info(f"\n📝 Comments Extraction:")
        logger.info(f"   Current: {current.comments_extracted}")
        logger.info(f"   Enhanced: {enhanced.comments_extracted}")
        logger.info(f"   Difference: {comments_improvement:+d}")
        
        # Efficiency comparison (comments per scroll)
        current_efficiency = current.comments_extracted / max(current.scroll_attempts, 1)
        enhanced_efficiency = enhanced.comments_extracted / max(enhanced.scroll_attempts, 1)
        efficiency_improvement = ((enhanced_efficiency - current_efficiency) / current_efficiency) * 100
        
        logger.info(f"\n⚡ Scroll Efficiency (comments per scroll):")
        logger.info(f"   Current: {current_efficiency:.2f}")
        logger.info(f"   Enhanced: {enhanced_efficiency:.2f}")
        logger.info(f"   Improvement: {efficiency_improvement:+.1f}%")
        
        # Success rate comparison
        success_improvement = enhanced.success_rate - current.success_rate
        logger.info(f"\n🎯 Success Rate:")
        logger.info(f"   Current: {current.success_rate:.1f}%")
        logger.info(f"   Enhanced: {enhanced.success_rate:.1f}%")
        logger.info(f"   Improvement: {success_improvement:+.1f}%")
        
        # Overall assessment
        logger.info(f"\n📊 Overall Assessment:")
        
        improvements = []
        if time_improvement > 0:
            improvements.append(f"⚡ {time_improvement:.1f}% faster")
        if comments_improvement > 0:
            improvements.append(f"📈 {comments_improvement} more comments")
        if efficiency_improvement > 0:
            improvements.append(f"🎯 {efficiency_improvement:.1f}% more efficient")
        if success_improvement > 0:
            improvements.append(f"✅ {success_improvement:.1f}% higher success rate")
        
        if improvements:
            logger.info("   Enhanced scraper advantages:")
            for improvement in improvements:
                logger.info(f"     {improvement}")
        else:
            logger.info("   No significant improvements detected")
        
        # Recommendations
        logger.info(f"\n💡 Recommendations:")
        
        if enhanced.total_time < current.total_time:
            logger.info("   ✅ Enhanced scraper is faster - recommended for production")
        
        if enhanced.comments_extracted > current.comments_extracted:
            logger.info("   ✅ Enhanced scraper extracts more comments - better data quality")
        
        if enhanced_efficiency > current_efficiency:
            logger.info("   ✅ Enhanced scraper is more efficient - less resource usage")
        
        if enhanced.error_count < current.error_count:
            logger.info("   ✅ Enhanced scraper is more reliable - fewer errors")


async def run_performance_demo():
    """Run the performance comparison demo"""
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Test configuration
    test_post_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890"  # Replace with actual URL
    max_comments_to_test = 50  # Smaller number for demo
    
    logger.info("🧪 Facebook Scraper Performance Comparison Demo")
    logger.info(f"📋 Test URL: {test_post_url}")
    logger.info(f"🎯 Target comments: {max_comments_to_test}")
    logger.info("")
    
    try:
        comparison = PerformanceComparison()
        results = await comparison.run_comparison(test_post_url, max_comments_to_test)
        
        logger.info("\n🎉 Performance comparison completed successfully!")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error running performance comparison: {e}")
        return None


if __name__ == "__main__":
    # Run the demo
    asyncio.run(run_performance_demo())
