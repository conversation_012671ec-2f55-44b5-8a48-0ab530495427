"""
Import checker and fixer for Facebook API Interception modules
Checks all imports and identifies missing dependencies
"""
import sys
import os
import importlib
from pathlib import Path
from loguru import logger

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))


class ImportChecker:
    """Check and fix import issues"""
    
    def __init__(self):
        self.modules_to_check = [
            'facebook_comment_interceptor',
            'stealth_browser_manager', 
            'network_monitor',
            'smart_scrolling',
            'facebook_data_parser',
            'facebook_scraper_service'
        ]
        
        self.import_results = {}
        
    def check_module_imports(self, module_name: str) -> dict:
        """Check imports for a specific module"""
        logger.info(f"Checking imports for: {module_name}")
        
        result = {
            'module': module_name,
            'importable': False,
            'missing_dependencies': [],
            'errors': []
        }
        
        try:
            # Try to import the module
            module = importlib.import_module(module_name)
            result['importable'] = True
            logger.info(f"✅ {module_name} imported successfully")
            
        except ImportError as e:
            result['errors'].append(f"ImportError: {str(e)}")
            logger.error(f"❌ {module_name} import failed: {e}")
            
            # Try to identify missing dependencies
            error_str = str(e)
            if "No module named" in error_str:
                missing_module = error_str.split("'")[1] if "'" in error_str else "unknown"
                result['missing_dependencies'].append(missing_module)
                
        except Exception as e:
            result['errors'].append(f"Other error: {str(e)}")
            logger.error(f"❌ {module_name} failed with error: {e}")
        
        return result
    
    def check_zendriver_availability(self) -> bool:
        """Check if zendriver is available"""
        logger.info("Checking zendriver availability...")
        
        zendriver_path = Path(__file__).parent / '..' / 'zendriver_local'
        
        if zendriver_path.exists():
            logger.info(f"✅ zendriver_local found at: {zendriver_path}")
            
            # Add to path
            sys.path.insert(0, str(zendriver_path))
            
            try:
                import zendriver
                logger.info("✅ zendriver imported successfully")
                return True
            except Exception as e:
                logger.error(f"❌ zendriver import failed: {e}")
                return False
        else:
            logger.error(f"❌ zendriver_local not found at: {zendriver_path}")
            return False
    
    def check_all_modules(self):
        """Check all modules"""
        logger.info("🔍 Starting import check for all modules...")
        
        # First check zendriver
        zendriver_ok = self.check_zendriver_availability()
        
        # Check each module
        for module_name in self.modules_to_check:
            result = self.check_module_imports(module_name)
            self.import_results[module_name] = result
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate import check report"""
        logger.info("\n" + "="*60)
        logger.info("📊 IMPORT CHECK REPORT")
        logger.info("="*60)
        
        total_modules = len(self.import_results)
        successful_imports = sum(1 for result in self.import_results.values() if result['importable'])
        
        logger.info(f"Total modules checked: {total_modules}")
        logger.info(f"Successful imports: {successful_imports}")
        logger.info(f"Failed imports: {total_modules - successful_imports}")
        
        # Detailed results
        for module_name, result in self.import_results.items():
            status = "✅ OK" if result['importable'] else "❌ FAILED"
            logger.info(f"\n{module_name}: {status}")
            
            if result['errors']:
                for error in result['errors']:
                    logger.error(f"  Error: {error}")
            
            if result['missing_dependencies']:
                for dep in result['missing_dependencies']:
                    logger.warning(f"  Missing: {dep}")
        
        # Recommendations
        logger.info("\n" + "="*60)
        logger.info("🔧 RECOMMENDATIONS")
        logger.info("="*60)
        
        all_missing_deps = set()
        for result in self.import_results.values():
            all_missing_deps.update(result['missing_dependencies'])
        
        if all_missing_deps:
            logger.info("Install missing dependencies:")
            for dep in sorted(all_missing_deps):
                if dep not in ['zendriver', 'cdp']:  # These are local modules
                    logger.info(f"  pip install {dep}")
        
        # Check for common issues
        failed_modules = [name for name, result in self.import_results.items() if not result['importable']]
        
        if failed_modules:
            logger.info("\nCommon fixes:")
            logger.info("1. Ensure zendriver_local is in the correct path")
            logger.info("2. Check that all required files exist")
            logger.info("3. Verify Python path configuration")
            
        if successful_imports == total_modules:
            logger.info("🎉 All modules imported successfully!")
        else:
            logger.warning(f"⚠️ {total_modules - successful_imports} modules need attention")
    
    def create_simple_test(self):
        """Create a simple test to verify functionality"""
        logger.info("🧪 Creating simple functionality test...")
        
        test_code = '''
"""
Simple functionality test for API interception modules
"""
import asyncio
import sys
import os

# Add path
sys.path.insert(0, os.path.dirname(__file__))

async def simple_test():
    """Simple test function"""
    try:
        # Test basic imports
        from facebook_comment_interceptor import InterceptionConfig
        from stealth_browser_manager import create_facebook_profile, ProxyConfig
        from network_monitor import create_facebook_network_monitor
        from smart_scrolling import create_smart_scroller
        from facebook_data_parser import create_facebook_parser
        
        print("✅ All imports successful")
        
        # Test basic object creation
        config = InterceptionConfig()
        print(f"✅ InterceptionConfig created: max_comments={config.max_comments}")
        
        proxy_config = ProxyConfig(proxy_type='none')
        print(f"✅ ProxyConfig created: type={proxy_config.proxy_type}")
        
        profile = create_facebook_profile('test', 'Test Profile', './test', proxy_config)
        print(f"✅ Profile created: {profile.profile_name}")
        
        monitor = create_facebook_network_monitor()
        print("✅ Network monitor created")
        
        scroller = create_smart_scroller()
        print("✅ Smart scroller created")
        
        parser = create_facebook_parser()
        print("✅ Data parser created")
        
        print("🎉 All basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(simple_test())
'''
        
        test_file = Path(__file__).parent / 'simple_functionality_test.py'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_code)
        
        logger.info(f"📝 Simple test created: {test_file}")
        logger.info("Run it with: python simple_functionality_test.py")


def main():
    """Main function"""
    logger.info("🚀 Starting import checker...")
    
    checker = ImportChecker()
    checker.check_all_modules()
    checker.create_simple_test()
    
    logger.info("✅ Import check completed!")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    main()
