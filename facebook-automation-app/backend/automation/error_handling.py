"""
ErrorHandling - Comprehensive error handling and recovery system for hybrid architecture
"""
import asyncio
import time
import traceback
import sys
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger
import json


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories"""
    NETWORK = "network"
    SERVICE = "service"
    BROWSER = "browser"
    MEMORY = "memory"
    TIMEOUT = "timeout"
    AUTHENTICATION = "authentication"
    PARSING = "parsing"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Error context information"""
    error_id: str
    timestamp: float
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    operation: str
    error_message: str
    stack_trace: Optional[str] = None
    metadata: Dict[str, Any] = None
    recovery_attempted: bool = False
    recovery_successful: bool = False


class ErrorRecoveryStrategy:
    """Base class for error recovery strategies"""
    
    def __init__(self, name: str, max_attempts: int = 3, backoff_factor: float = 2.0):
        self.name = name
        self.max_attempts = max_attempts
        self.backoff_factor = backoff_factor
        self.attempt_count = 0
        
    async def can_recover(self, error_context: ErrorContext) -> bool:
        """Check if this strategy can handle the error"""
        return True
    
    async def attempt_recovery(self, error_context: ErrorContext) -> bool:
        """Attempt error recovery"""
        self.attempt_count += 1
        
        if self.attempt_count > self.max_attempts:
            return False
        
        # Calculate backoff delay
        delay = (self.backoff_factor ** (self.attempt_count - 1))
        await asyncio.sleep(delay)
        
        return await self._execute_recovery(error_context)
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        """Execute specific recovery logic - to be implemented by subclasses"""
        raise NotImplementedError
    
    def reset(self):
        """Reset attempt counter"""
        self.attempt_count = 0


class NetworkErrorRecovery(ErrorRecoveryStrategy):
    """Recovery strategy for network-related errors"""
    
    def __init__(self):
        super().__init__("NetworkErrorRecovery", max_attempts=5, backoff_factor=1.5)
    
    async def can_recover(self, error_context: ErrorContext) -> bool:
        return error_context.category == ErrorCategory.NETWORK
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        try:
            logger.info(f"Attempting network recovery (attempt {self.attempt_count})")
            
            # Simulate network connectivity check
            # In real implementation, this would check actual network connectivity
            await asyncio.sleep(1)
            
            # For demo purposes, succeed after 2 attempts
            return self.attempt_count >= 2
            
        except Exception as e:
            logger.error(f"Network recovery failed: {e}")
            return False


class ServiceErrorRecovery(ErrorRecoveryStrategy):
    """Recovery strategy for service-related errors"""
    
    def __init__(self, service_manager):
        super().__init__("ServiceErrorRecovery", max_attempts=3, backoff_factor=2.0)
        self.service_manager = service_manager
    
    async def can_recover(self, error_context: ErrorContext) -> bool:
        return error_context.category == ErrorCategory.SERVICE
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        try:
            logger.info(f"Attempting service recovery for {error_context.component}")
            
            # Attempt to restart or reconnect to service
            if hasattr(self.service_manager, 'restart_service'):
                success = await self.service_manager.restart_service(error_context.component)
                return success
            
            # Fallback: just wait and hope service recovers
            await asyncio.sleep(5)
            return True
            
        except Exception as e:
            logger.error(f"Service recovery failed: {e}")
            return False


class BrowserErrorRecovery(ErrorRecoveryStrategy):
    """Recovery strategy for browser-related errors"""
    
    def __init__(self, browser_manager):
        super().__init__("BrowserErrorRecovery", max_attempts=3, backoff_factor=1.5)
        self.browser_manager = browser_manager
    
    async def can_recover(self, error_context: ErrorContext) -> bool:
        return error_context.category == ErrorCategory.BROWSER
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        try:
            logger.info(f"Attempting browser recovery")
            
            # Extract profile ID from metadata
            profile_id = error_context.metadata.get("profile_id") if error_context.metadata else None
            
            if profile_id and self.browser_manager:
                # Close and restart browser
                await self.browser_manager.close_browser(profile_id)
                await asyncio.sleep(2)
                success = await self.browser_manager.launch_browser(profile_id)
                return success
            
            return False
            
        except Exception as e:
            logger.error(f"Browser recovery failed: {e}")
            return False


class MemoryErrorRecovery(ErrorRecoveryStrategy):
    """Recovery strategy for memory-related errors"""
    
    def __init__(self):
        super().__init__("MemoryErrorRecovery", max_attempts=2, backoff_factor=1.0)
    
    async def can_recover(self, error_context: ErrorContext) -> bool:
        return error_context.category == ErrorCategory.MEMORY
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        try:
            logger.info("Attempting memory recovery")
            
            # Force garbage collection
            import gc
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
            
            # Clear caches if available
            # This would be implemented based on specific cache systems
            
            # Check if memory pressure is reduced
            import psutil
            memory_percent = psutil.virtual_memory().percent
            
            return memory_percent < 90  # Consider recovered if memory usage < 90%
            
        except Exception as e:
            logger.error(f"Memory recovery failed: {e}")
            return False


class FallbackRecovery(ErrorRecoveryStrategy):
    """Fallback recovery strategy - switches to legacy mode"""
    
    def __init__(self, hybrid_coordinator):
        super().__init__("FallbackRecovery", max_attempts=1, backoff_factor=1.0)
        self.hybrid_coordinator = hybrid_coordinator
    
    async def can_recover(self, error_context: ErrorContext) -> bool:
        # Fallback can handle any error as last resort
        return True
    
    async def _execute_recovery(self, error_context: ErrorContext) -> bool:
        try:
            logger.warning("Activating fallback recovery - switching to legacy mode")
            
            if self.hybrid_coordinator:
                # Enable fallback mode
                self.hybrid_coordinator.fallback_mode = True
                logger.info("Fallback mode activated")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Fallback recovery failed: {e}")
            return False


class ErrorHandler:
    """Comprehensive error handler with recovery strategies"""
    
    def __init__(self):
        self.recovery_strategies: List[ErrorRecoveryStrategy] = []
        self.error_history: List[ErrorContext] = []
        self.error_callbacks: List[Callable] = []
        self.max_history_size = 1000
        
        # Error statistics
        self.stats = {
            "total_errors": 0,
            "recovered_errors": 0,
            "unrecovered_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "recovery_success_rate": 0.0
        }
    
    def register_recovery_strategy(self, strategy: ErrorRecoveryStrategy):
        """Register error recovery strategy"""
        self.recovery_strategies.append(strategy)
        logger.info(f"Registered recovery strategy: {strategy.name}")
    
    def register_error_callback(self, callback: Callable):
        """Register error callback for notifications"""
        self.error_callbacks.append(callback)
    
    async def handle_error(
        self,
        error: Exception,
        component: str,
        operation: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Handle error with recovery attempts"""
        
        # Create error context
        error_context = ErrorContext(
            error_id=f"ERR_{int(time.time() * 1000)}",
            timestamp=time.time(),
            severity=severity,
            category=category,
            component=component,
            operation=operation,
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            metadata=metadata or {}
        )
        
        # Log error
        self._log_error(error_context)
        
        # Update statistics
        self._update_error_stats(error_context)
        
        # Store in history
        self.error_history.append(error_context)
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
        
        # Notify callbacks
        await self._notify_error_callbacks(error_context)
        
        # Attempt recovery
        recovery_successful = await self._attempt_recovery(error_context)
        
        # Update error context with recovery result
        error_context.recovery_attempted = True
        error_context.recovery_successful = recovery_successful
        
        if recovery_successful:
            self.stats["recovered_errors"] += 1
            logger.info(f"Error {error_context.error_id} recovered successfully")
        else:
            self.stats["unrecovered_errors"] += 1
            logger.error(f"Error {error_context.error_id} could not be recovered")
        
        # Update recovery success rate
        self.stats["recovery_success_rate"] = (
            self.stats["recovered_errors"] / self.stats["total_errors"]
            if self.stats["total_errors"] > 0 else 0.0
        )
        
        return recovery_successful
    
    def _log_error(self, error_context: ErrorContext):
        """Log error with appropriate level"""
        log_message = (
            f"[{error_context.error_id}] {error_context.component}.{error_context.operation}: "
            f"{error_context.error_message}"
        )
        
        if error_context.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_context.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_context.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _update_error_stats(self, error_context: ErrorContext):
        """Update error statistics"""
        self.stats["total_errors"] += 1
        
        # Update category stats
        category_key = error_context.category.value
        self.stats["errors_by_category"][category_key] = (
            self.stats["errors_by_category"].get(category_key, 0) + 1
        )
        
        # Update severity stats
        severity_key = error_context.severity.value
        self.stats["errors_by_severity"][severity_key] = (
            self.stats["errors_by_severity"].get(severity_key, 0) + 1
        )
    
    async def _notify_error_callbacks(self, error_context: ErrorContext):
        """Notify registered error callbacks"""
        for callback in self.error_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(error_context)
                else:
                    callback(error_context)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")
    
    async def _attempt_recovery(self, error_context: ErrorContext) -> bool:
        """Attempt error recovery using registered strategies"""
        for strategy in self.recovery_strategies:
            try:
                if await strategy.can_recover(error_context):
                    logger.info(f"Attempting recovery with strategy: {strategy.name}")
                    
                    if await strategy.attempt_recovery(error_context):
                        logger.info(f"Recovery successful with strategy: {strategy.name}")
                        strategy.reset()  # Reset for next use
                        return True
                    else:
                        logger.warning(f"Recovery failed with strategy: {strategy.name}")
                        
            except Exception as e:
                logger.error(f"Error in recovery strategy {strategy.name}: {e}")
        
        return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error handling statistics"""
        return {
            **self.stats,
            "recent_errors": [
                {
                    "error_id": ec.error_id,
                    "timestamp": ec.timestamp,
                    "component": ec.component,
                    "operation": ec.operation,
                    "severity": ec.severity.value,
                    "category": ec.category.value,
                    "recovered": ec.recovery_successful
                }
                for ec in self.error_history[-10:]  # Last 10 errors
            ]
        }
    
    def get_error_trends(self) -> Dict[str, Any]:
        """Analyze error trends"""
        if not self.error_history:
            return {"status": "no_data"}
        
        # Analyze recent errors (last hour)
        current_time = time.time()
        recent_errors = [
            ec for ec in self.error_history
            if current_time - ec.timestamp < 3600  # Last hour
        ]
        
        # Calculate error rate
        error_rate = len(recent_errors) / 60 if recent_errors else 0  # errors per minute
        
        # Most common error categories
        category_counts = {}
        for error in recent_errors:
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        most_common_category = max(category_counts.items(), key=lambda x: x[1]) if category_counts else None
        
        # Recovery effectiveness
        recent_recovery_rate = (
            sum(1 for e in recent_errors if e.recovery_successful) / len(recent_errors)
            if recent_errors else 0.0
        )
        
        return {
            "status": "analyzed",
            "recent_error_count": len(recent_errors),
            "error_rate_per_minute": error_rate,
            "most_common_category": most_common_category[0] if most_common_category else None,
            "recent_recovery_rate": recent_recovery_rate,
            "trend": "increasing" if error_rate > 1 else "stable" if error_rate > 0.1 else "low"
        }


class GracefulDegradationManager:
    """Manages graceful degradation strategies"""
    
    def __init__(self):
        self.degradation_levels = {
            "normal": 0,
            "reduced_performance": 1,
            "limited_functionality": 2,
            "emergency_mode": 3
        }
        self.current_level = 0
        self.degradation_triggers = {}
        
    def register_degradation_trigger(
        self,
        trigger_name: str,
        condition_check: Callable,
        degradation_level: int,
        recovery_check: Callable
    ):
        """Register degradation trigger"""
        self.degradation_triggers[trigger_name] = {
            "condition_check": condition_check,
            "degradation_level": degradation_level,
            "recovery_check": recovery_check,
            "active": False
        }
    
    async def check_degradation_conditions(self) -> bool:
        """Check if degradation is needed"""
        max_degradation_level = 0
        active_triggers = []
        
        for trigger_name, trigger in self.degradation_triggers.items():
            try:
                # Check if trigger condition is met
                if await trigger["condition_check"]():
                    if not trigger["active"]:
                        logger.warning(f"Degradation trigger activated: {trigger_name}")
                        trigger["active"] = True
                    
                    active_triggers.append(trigger_name)
                    max_degradation_level = max(max_degradation_level, trigger["degradation_level"])
                
                # Check for recovery
                elif trigger["active"] and await trigger["recovery_check"]():
                    logger.info(f"Degradation trigger recovered: {trigger_name}")
                    trigger["active"] = False
                    
            except Exception as e:
                logger.error(f"Error checking degradation trigger {trigger_name}: {e}")
        
        # Update degradation level
        if max_degradation_level != self.current_level:
            await self._apply_degradation(max_degradation_level, active_triggers)
        
        return max_degradation_level > 0
    
    async def _apply_degradation(self, level: int, active_triggers: List[str]):
        """Apply degradation level"""
        old_level = self.current_level
        self.current_level = level
        
        level_names = {v: k for k, v in self.degradation_levels.items()}
        level_name = level_names.get(level, "unknown")
        
        logger.warning(
            f"Degradation level changed: {old_level} -> {level} ({level_name}). "
            f"Active triggers: {', '.join(active_triggers)}"
        )
        
        # Apply specific degradation strategies based on level
        if level == 1:  # Reduced performance
            await self._apply_reduced_performance()
        elif level == 2:  # Limited functionality
            await self._apply_limited_functionality()
        elif level == 3:  # Emergency mode
            await self._apply_emergency_mode()
    
    async def _apply_reduced_performance(self):
        """Apply reduced performance mode"""
        logger.info("Applying reduced performance mode")
        # Reduce concurrent operations, increase timeouts, etc.
    
    async def _apply_limited_functionality(self):
        """Apply limited functionality mode"""
        logger.info("Applying limited functionality mode")
        # Disable non-essential features, use simpler algorithms, etc.
    
    async def _apply_emergency_mode(self):
        """Apply emergency mode"""
        logger.critical("Applying emergency mode")
        # Minimal functionality only, aggressive resource conservation


async def setup_error_handling_system(
    browser_manager=None,
    service_manager=None,
    hybrid_coordinator=None
) -> ErrorHandler:
    """Setup comprehensive error handling system"""
    
    error_handler = ErrorHandler()
    
    # Register recovery strategies
    error_handler.register_recovery_strategy(NetworkErrorRecovery())
    
    if service_manager:
        error_handler.register_recovery_strategy(ServiceErrorRecovery(service_manager))
    
    if browser_manager:
        error_handler.register_recovery_strategy(BrowserErrorRecovery(browser_manager))
    
    error_handler.register_recovery_strategy(MemoryErrorRecovery())
    
    if hybrid_coordinator:
        error_handler.register_recovery_strategy(FallbackRecovery(hybrid_coordinator))
    
    logger.info("Error handling system setup completed")
    return error_handler


# Decorator for automatic error handling
def handle_errors(
    component: str,
    operation: str = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.UNKNOWN
):
    """Decorator for automatic error handling"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Get error handler from global context or create one
                error_handler = getattr(wrapper, '_error_handler', None)
                if not error_handler:
                    error_handler = ErrorHandler()
                
                operation_name = operation or func.__name__
                
                # Handle the error
                recovered = await error_handler.handle_error(
                    error=e,
                    component=component,
                    operation=operation_name,
                    severity=severity,
                    category=category
                )
                
                if not recovered:
                    raise e  # Re-raise if not recovered
                
                # Return None or default value if recovered
                return None
        
        return wrapper
    return decorator
