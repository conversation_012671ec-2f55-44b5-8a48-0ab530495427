"""
Demo script for Facebook API Interception
Shows the complete workflow with the new implementation
"""
import asyncio
import time
import json
from loguru import logger
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from facebook_scraper_service import create_facebook_scraper_service


async def demo_facebook_api_interception():
    """Demo the complete Facebook API interception workflow"""
    
    logger.info("🚀 Facebook API Interception Demo")
    logger.info("=" * 50)
    
    # Configuration
    profile_config = {
        'profile_id': 'demo_profile_001',
        'profile_name': 'Facebook API Demo',
        'user_data_dir': './demo_browser_profiles/facebook_demo',
        'proxy': None  # No proxy for demo
    }
    
    # Test post URL (you can change this to any Facebook post)
    post_url = "https://www.facebook.com/groups/591054007361950/posts/8068194536314616/"
    
    scraper_service = None
    
    try:
        # Step 1: Create scraper service
        logger.info("📦 Creating Facebook scraper service...")
        scraper_service = await create_facebook_scraper_service("demo_data")
        
        # Step 2: Show configuration
        logger.info("⚙️ Configuration:")
        logger.info(f"   Profile ID: {profile_config['profile_id']}")
        logger.info(f"   Profile Name: {profile_config['profile_name']}")
        logger.info(f"   User Data Dir: {profile_config['user_data_dir']}")
        logger.info(f"   Post URL: {post_url}")
        
        # Step 3: Start scraping
        logger.info("🎯 Starting Facebook comment scraping...")
        logger.info("   Method: API Interception with Zendriver")
        logger.info("   Browser: Visible with Off-screen positioning")
        logger.info("   Max Comments: 100")
        
        start_time = time.time()
        
        result = await scraper_service.scrape_facebook_post_uids(
            profile_config=profile_config,
            post_url=post_url,
            max_comments=100
        )
        
        total_time = time.time() - start_time
        
        # Step 4: Process results
        logger.info("📊 Processing results...")
        
        if result.get("success", False):
            results = result["results"]
            
            # Basic metrics
            comments_extracted = results.get("total_comments_extracted", 0)
            uids_found = results.get("total_uids_found", 0)
            new_uids = results.get("new_uids", [])
            duplicates = results.get("duplicate_count", 0)
            scraping_time = results.get("scraping_time", 0)
            
            # API interception metrics
            api_stats = results.get("api_interception_stats", {})
            api_calls = api_stats.get("api_calls_intercepted", 0)
            scroll_attempts = api_stats.get("scroll_attempts", 0)
            processing_time = api_stats.get("processing_time", 0)
            
            # Performance metrics
            comments_per_second = comments_extracted / scraping_time if scraping_time > 0 else 0
            uids_per_second = uids_found / scraping_time if scraping_time > 0 else 0
            api_efficiency = comments_extracted / api_calls if api_calls > 0 else 0
            
            # Display results
            logger.info("✅ SCRAPING SUCCESSFUL!")
            logger.info("=" * 50)
            logger.info("📈 EXTRACTION METRICS:")
            logger.info(f"   💬 Comments extracted: {comments_extracted}")
            logger.info(f"   👥 Total UIDs found: {uids_found}")
            logger.info(f"   🆕 New UIDs: {len(new_uids)}")
            logger.info(f"   🔄 Duplicates: {duplicates}")
            
            logger.info("\n📡 API INTERCEPTION METRICS:")
            logger.info(f"   🔗 API calls intercepted: {api_calls}")
            logger.info(f"   📜 Scroll attempts: {scroll_attempts}")
            logger.info(f"   ⚡ API efficiency: {api_efficiency:.2f} comments/call")
            
            logger.info("\n⏱️ PERFORMANCE METRICS:")
            logger.info(f"   🕐 Total time: {total_time:.2f}s")
            logger.info(f"   🕐 Scraping time: {scraping_time:.2f}s")
            logger.info(f"   🕐 Processing time: {processing_time:.2f}s")
            logger.info(f"   🚀 Comments/second: {comments_per_second:.2f}")
            logger.info(f"   🚀 UIDs/second: {uids_per_second:.2f}")
            
            # Show sample UIDs
            if new_uids:
                logger.info("\n🔢 SAMPLE NEW UIDs:")
                for i, uid in enumerate(new_uids[:10]):
                    logger.info(f"   {i+1:2d}. {uid}")
                if len(new_uids) > 10:
                    logger.info(f"   ... and {len(new_uids) - 10} more")
            
            # Show sample user details
            user_details = results.get("user_details", [])
            if user_details:
                logger.info("\n👤 SAMPLE USER DETAILS:")
                for i, user in enumerate(user_details[:5]):
                    uid = user.get('uid', 'N/A')
                    username = user.get('username', 'N/A')
                    is_anon = user.get('is_anonymous', False)
                    anon_text = " (Anonymous)" if is_anon else ""
                    logger.info(f"   {i+1}. UID: {uid}, Name: {username}{anon_text}")
            
            # Performance evaluation
            logger.info("\n🎯 PERFORMANCE EVALUATION:")
            if comments_per_second > 10:
                logger.info("   🚀 EXCELLENT performance!")
            elif comments_per_second > 5:
                logger.info("   ✅ GOOD performance")
            elif comments_per_second > 2:
                logger.info("   👍 ACCEPTABLE performance")
            else:
                logger.info("   ⚠️ Performance could be improved")
            
            if api_efficiency > 20:
                logger.info("   🎯 EXCELLENT API efficiency!")
            elif api_efficiency > 10:
                logger.info("   ✅ GOOD API efficiency")
            else:
                logger.info("   ⚠️ API efficiency could be improved")
            
            # Deduplication stats
            dedup_stats = results.get("deduplication_stats", {})
            if dedup_stats:
                logger.info("\n🔄 DEDUPLICATION STATS:")
                total_unique = dedup_stats.get("total_unique_uids", 0)
                logger.info(f"   📊 Total unique UIDs in system: {total_unique}")
            
            # Save detailed results
            demo_results = {
                "demo_timestamp": time.time(),
                "post_url": post_url,
                "profile_config": profile_config,
                "results": results,
                "performance_analysis": {
                    "total_time": total_time,
                    "comments_per_second": comments_per_second,
                    "uids_per_second": uids_per_second,
                    "api_efficiency": api_efficiency
                }
            }
            
            results_file = f"demo_results_{int(time.time())}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(demo_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"\n💾 Detailed results saved to: {results_file}")
            
        else:
            # Handle failure
            error_msg = result.get("error", "Unknown error")
            logger.error("❌ SCRAPING FAILED!")
            logger.error(f"   Error: {error_msg}")
            logger.error(f"   Time elapsed: {total_time:.2f}s")
            
            # Show troubleshooting tips
            logger.info("\n🔧 TROUBLESHOOTING TIPS:")
            logger.info("   1. Check if the Facebook post URL is accessible")
            logger.info("   2. Ensure you have proper Facebook login cookies")
            logger.info("   3. Try with a different post URL")
            logger.info("   4. Check network connectivity")
            
    except Exception as e:
        logger.error(f"❌ Demo failed with exception: {e}")
        logger.error("🔧 This might be due to:")
        logger.error("   - Missing dependencies")
        logger.error("   - Network issues")
        logger.error("   - Facebook access restrictions")
        
    finally:
        # Cleanup
        if scraper_service:
            logger.info("🧹 Cleaning up resources...")
            await scraper_service.cleanup()
        
        logger.info("👋 Demo completed!")


async def interactive_demo():
    """Interactive demo with user input"""
    
    print("\n" + "="*60)
    print("🚀 Facebook API Interception Interactive Demo")
    print("="*60)
    
    # Get user input
    print("\n📝 Configuration:")
    
    post_url = input("Enter Facebook post URL (or press ENTER for default): ").strip()
    if not post_url:
        post_url = "https://www.facebook.com/groups/591054007361950/posts/8068194536314616/"
        print(f"Using default URL: {post_url}")
    
    max_comments = input("Enter max comments to extract (default 100): ").strip()
    max_comments = int(max_comments) if max_comments.isdigit() else 100
    
    profile_name = input("Enter profile name (default 'Interactive Demo'): ").strip()
    profile_name = profile_name or "Interactive Demo"
    
    # Run demo with user configuration
    profile_config = {
        'profile_id': 'interactive_demo',
        'profile_name': profile_name,
        'user_data_dir': './demo_browser_profiles/interactive_demo',
        'proxy': None
    }
    
    scraper_service = None
    
    try:
        logger.info(f"🎯 Starting scraping with {max_comments} max comments...")
        
        scraper_service = await create_facebook_scraper_service("interactive_demo_data")
        
        result = await scraper_service.scrape_facebook_post_uids(
            profile_config=profile_config,
            post_url=post_url,
            max_comments=max_comments
        )
        
        # Show simplified results
        if result.get("success", False):
            results = result["results"]
            print(f"\n✅ Success! Extracted {results.get('total_uids_found', 0)} UIDs from {results.get('total_comments_extracted', 0)} comments")
            print(f"⏱️ Time: {results.get('scraping_time', 0):.2f}s")
            print(f"📡 API calls: {results.get('api_interception_stats', {}).get('api_calls_intercepted', 0)}")
        else:
            print(f"\n❌ Failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"\n❌ Error: {e}")
    
    finally:
        if scraper_service:
            await scraper_service.cleanup()


def main():
    """Main function"""
    print("Choose demo mode:")
    print("1. Automatic demo with default settings")
    print("2. Interactive demo with custom settings")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        asyncio.run(interactive_demo())
    else:
        asyncio.run(demo_facebook_api_interception())


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    main()
