"""
ComprehensiveIntegrationSuite - Complete integration and error handling test suite
"""
import asyncio
import time
import json
import os
from typing import Dict, Any, List
from loguru import logger

from .integration_testing import IntegrationTestRunner
from .recovery_testing import RecoveryTester
from .error_handling import setup_error_handling_system
from .communication_test import CommunicationTester


class ComprehensiveIntegrationSuite:
    """Complete integration and error handling test suite"""
    
    def __init__(self, output_dir: str = "integration_results"):
        self.output_dir = output_dir
        self.results = {}
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize test components
        self.integration_tester = IntegrationTestRunner()
        self.recovery_tester = RecoveryTester()
        self.communication_tester = CommunicationTester()
        
    async def run_complete_integration_suite(self) -> Dict[str, Any]:
        """Run complete integration and error handling test suite"""
        logger.info("🧪 Starting Comprehensive Integration & Error Handling Test Suite")
        logger.info("="*100)
        
        suite_start_time = time.time()
        
        # Test execution plan
        test_plan = [
            ("Communication Integration Tests", self._run_communication_integration_tests),
            ("Component Integration Tests", self._run_component_integration_tests),
            ("Error Handling Tests", self._run_error_handling_tests),
            ("Recovery Mechanism Tests", self._run_recovery_tests),
            ("End-to-End Integration Tests", self._run_end_to_end_tests),
            ("Stress Integration Tests", self._run_stress_integration_tests)
        ]
        
        # Execute test plan
        for test_name, test_func in test_plan:
            logger.info(f"\n📋 Running {test_name}...")
            try:
                result = await test_func()
                self.results[test_name.lower().replace(" ", "_")] = result
                logger.info(f"✅ {test_name} completed successfully")
            except Exception as e:
                logger.error(f"❌ {test_name} failed: {e}")
                self.results[test_name.lower().replace(" ", "_")] = {"error": str(e)}
        
        suite_duration = time.time() - suite_start_time
        
        # Generate comprehensive report
        comprehensive_report = await self._generate_comprehensive_report(suite_duration)
        
        # Save all results
        await self._save_results()
        
        logger.info(f"\n🎉 Integration Test Suite completed in {suite_duration:.1f} seconds")
        logger.info(f"📁 Results saved to {self.output_dir}/")
        
        return comprehensive_report
    
    async def _run_communication_integration_tests(self) -> Dict[str, Any]:
        """Run communication integration tests"""
        return await self.communication_tester.run_all_tests()
    
    async def _run_component_integration_tests(self) -> Dict[str, Any]:
        """Run component integration tests"""
        # Filter integration tests for component-specific tests
        component_tests = [
            tc for tc in self.integration_tester.test_cases 
            if tc.test_type == "component"
        ]
        
        results = []
        for test_case in component_tests:
            await self.integration_tester._run_single_test(test_case)
        
        return {
            "component_tests": self.integration_tester.test_results,
            "summary": self._summarize_test_results(self.integration_tester.test_results)
        }
    
    async def _run_error_handling_tests(self) -> Dict[str, Any]:
        """Run error handling tests"""
        # Setup error handling system
        error_handler = await setup_error_handling_system()
        
        # Test various error scenarios
        error_scenarios = [
            {
                "name": "Network Error Handling",
                "error": ConnectionError("Simulated network error"),
                "component": "network_test",
                "operation": "connect",
                "expected_recovery": True
            },
            {
                "name": "Service Error Handling", 
                "error": Exception("Service unavailable"),
                "component": "service_test",
                "operation": "request",
                "expected_recovery": True
            },
            {
                "name": "Memory Error Handling",
                "error": MemoryError("Out of memory"),
                "component": "memory_test",
                "operation": "allocate",
                "expected_recovery": True
            }
        ]
        
        test_results = []
        for scenario in error_scenarios:
            try:
                start_time = time.time()
                
                # Handle the error
                recovered = await error_handler.handle_error(
                    error=scenario["error"],
                    component=scenario["component"],
                    operation=scenario["operation"]
                )
                
                duration = time.time() - start_time
                
                test_results.append({
                    "scenario": scenario["name"],
                    "recovered": recovered,
                    "expected_recovery": scenario["expected_recovery"],
                    "success": recovered == scenario["expected_recovery"],
                    "duration": duration
                })
                
            except Exception as e:
                test_results.append({
                    "scenario": scenario["name"],
                    "recovered": False,
                    "expected_recovery": scenario["expected_recovery"],
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "error_handling_tests": test_results,
            "error_statistics": error_handler.get_error_statistics(),
            "summary": self._summarize_test_results(test_results)
        }
    
    async def _run_recovery_tests(self) -> Dict[str, Any]:
        """Run recovery mechanism tests"""
        return await self.recovery_tester.run_recovery_tests()
    
    async def _run_end_to_end_tests(self) -> Dict[str, Any]:
        """Run end-to-end integration tests"""
        # Filter integration tests for workflow tests
        workflow_tests = [
            tc for tc in self.integration_tester.test_cases 
            if tc.test_type == "workflow"
        ]
        
        results = []
        for test_case in workflow_tests:
            await self.integration_tester._run_single_test(test_case)
        
        return {
            "end_to_end_tests": self.integration_tester.test_results,
            "summary": self._summarize_test_results(self.integration_tester.test_results)
        }
    
    async def _run_stress_integration_tests(self) -> Dict[str, Any]:
        """Run stress integration tests"""
        from .hybrid_integration import HybridServiceFactory
        
        stress_results = []
        
        # Test concurrent service initialization
        try:
            logger.info("Testing concurrent service initialization...")
            
            services = []
            start_time = time.time()
            
            # Create multiple services concurrently
            tasks = []
            for i in range(5):
                service = HybridServiceFactory.create_development_service()
                tasks.append(asyncio.create_task(service.initialize()))
                services.append(service)
            
            # Wait for all initializations
            init_results = await asyncio.gather(*tasks, return_exceptions=True)
            init_duration = time.time() - start_time
            
            successful_inits = sum(1 for r in init_results if r is True)
            
            stress_results.append({
                "test": "Concurrent Service Initialization",
                "services_created": len(services),
                "successful_initializations": successful_inits,
                "duration": init_duration,
                "success": successful_inits >= 3  # At least 60% success
            })
            
            # Cleanup services
            for service in services:
                try:
                    await service.cleanup()
                except:
                    pass
            
        except Exception as e:
            stress_results.append({
                "test": "Concurrent Service Initialization",
                "success": False,
                "error": str(e)
            })
        
        # Test rapid sequential operations
        try:
            logger.info("Testing rapid sequential operations...")
            
            service = HybridServiceFactory.create_development_service()
            if await service.initialize():
                
                start_time = time.time()
                operation_results = []
                
                # Perform rapid operations
                for i in range(10):
                    try:
                        result = await service.scrape_facebook_post_uids(
                            profile_id=f"stress_test_{i}",
                            post_url=f"https://facebook.com/test/stress/{i}"
                        )
                        operation_results.append(result.get("success", False))
                    except Exception as e:
                        operation_results.append(False)
                
                operation_duration = time.time() - start_time
                successful_operations = sum(operation_results)
                
                stress_results.append({
                    "test": "Rapid Sequential Operations",
                    "total_operations": len(operation_results),
                    "successful_operations": successful_operations,
                    "duration": operation_duration,
                    "operations_per_second": len(operation_results) / operation_duration,
                    "success": successful_operations >= 5  # At least 50% success
                })
                
                await service.cleanup()
            
        except Exception as e:
            stress_results.append({
                "test": "Rapid Sequential Operations",
                "success": False,
                "error": str(e)
            })
        
        return {
            "stress_integration_tests": stress_results,
            "summary": self._summarize_test_results(stress_results)
        }
    
    def _summarize_test_results(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize test results"""
        if not test_results:
            return {"total": 0, "passed": 0, "failed": 0, "success_rate": 0.0}
        
        total = len(test_results)
        passed = sum(1 for r in test_results if r.get("success", False) or r.get("status") == "PASSED")
        failed = total - passed
        success_rate = passed / total if total > 0 else 0.0
        
        return {
            "total": total,
            "passed": passed,
            "failed": failed,
            "success_rate": success_rate,
            "overall_status": "PASSED" if success_rate >= 0.8 else "FAILED"
        }
    
    async def _generate_comprehensive_report(self, suite_duration: float) -> Dict[str, Any]:
        """Generate comprehensive integration report"""
        try:
            report = {
                "timestamp": time.time(),
                "suite_duration": suite_duration,
                "executive_summary": self._generate_executive_summary(),
                "detailed_results": self.results,
                "integration_metrics": self._extract_integration_metrics(),
                "recommendations": self._generate_comprehensive_recommendations(),
                "system_readiness": self._assess_system_readiness()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return {"error": str(e)}
    
    def _generate_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary"""
        summary = {
            "overall_status": "unknown",
            "critical_issues": [],
            "integration_highlights": [],
            "system_readiness": "unknown"
        }
        
        try:
            critical_issues = []
            integration_highlights = []
            
            # Analyze each test category
            for test_category, results in self.results.items():
                if "error" in results:
                    critical_issues.append(f"{test_category}: {results['error']}")
                    continue
                
                summary_data = results.get("summary", {})
                success_rate = summary_data.get("success_rate", 0)
                
                if success_rate < 0.7:
                    critical_issues.append(f"{test_category}: Low success rate ({success_rate:.1%})")
                elif success_rate > 0.9:
                    integration_highlights.append(f"{test_category}: Excellent performance ({success_rate:.1%})")
            
            # Determine overall status
            if critical_issues:
                overall_status = "critical" if len(critical_issues) > 2 else "warning"
            else:
                overall_status = "healthy"
            
            # Determine system readiness
            if overall_status == "healthy" and len(integration_highlights) > 2:
                system_readiness = "production_ready"
            elif overall_status == "warning":
                system_readiness = "needs_optimization"
            else:
                system_readiness = "not_ready"
            
            summary.update({
                "overall_status": overall_status,
                "critical_issues": critical_issues,
                "integration_highlights": integration_highlights,
                "system_readiness": system_readiness
            })
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            summary["error"] = str(e)
        
        return summary
    
    def _extract_integration_metrics(self) -> Dict[str, Any]:
        """Extract key integration metrics"""
        metrics = {}
        
        try:
            # Communication metrics
            comm_results = self.results.get("communication_integration_tests", {})
            if "summary" in comm_results:
                metrics["communication_success_rate"] = comm_results["summary"].get("success_rate", 0)
            
            # Component integration metrics
            component_results = self.results.get("component_integration_tests", {})
            if "summary" in component_results:
                metrics["component_integration_success_rate"] = component_results["summary"].get("success_rate", 0)
            
            # Error handling metrics
            error_results = self.results.get("error_handling_tests", {})
            if "summary" in error_results:
                metrics["error_handling_success_rate"] = error_results["summary"].get("success_rate", 0)
            
            # Recovery metrics
            recovery_results = self.results.get("recovery_mechanism_tests", {})
            if "recovery_statistics" in recovery_results:
                recovery_stats = recovery_results["recovery_statistics"]
                metrics["overall_recovery_rate"] = recovery_stats.get("overall_recovery_rate", 0)
            
            # End-to-end metrics
            e2e_results = self.results.get("end-to-end_integration_tests", {})
            if "summary" in e2e_results:
                metrics["end_to_end_success_rate"] = e2e_results["summary"].get("success_rate", 0)
            
            # Stress test metrics
            stress_results = self.results.get("stress_integration_tests", {})
            if "summary" in stress_results:
                metrics["stress_test_success_rate"] = stress_results["summary"].get("success_rate", 0)
            
        except Exception as e:
            logger.error(f"Error extracting integration metrics: {e}")
            metrics["extraction_error"] = str(e)
        
        return metrics
    
    def _generate_comprehensive_recommendations(self) -> List[str]:
        """Generate comprehensive recommendations"""
        recommendations = []
        
        try:
            # Analyze results and generate specific recommendations
            for test_category, results in self.results.items():
                if "error" in results:
                    recommendations.append(f"Fix {test_category} - critical error: {results['error']}")
                    continue
                
                summary_data = results.get("summary", {})
                success_rate = summary_data.get("success_rate", 0)
                
                if success_rate < 0.8:
                    recommendations.append(f"Improve {test_category} - success rate below threshold")
            
            # General recommendations based on overall performance
            executive_summary = self._generate_executive_summary()
            
            if executive_summary.get("overall_status") == "critical":
                recommendations.insert(0, "CRITICAL: Address all critical issues before production deployment")
            elif executive_summary.get("overall_status") == "warning":
                recommendations.insert(0, "WARNING: Resolve integration issues before production")
            
            # Add strategic recommendations
            recommendations.extend([
                "Implement continuous integration testing pipeline",
                "Set up integration test monitoring and alerting",
                "Create integration test regression suite",
                "Document integration procedures and troubleshooting guides",
                "Establish integration SLAs and performance baselines"
            ])
            
        except Exception as e:
            recommendations.append(f"Error generating recommendations: {e}")
        
        return recommendations[:15]  # Limit to top 15
    
    def _assess_system_readiness(self) -> Dict[str, Any]:
        """Assess overall system readiness"""
        readiness = {
            "overall_score": 0,
            "category_scores": {},
            "readiness_status": "unknown",
            "production_ready": False
        }
        
        try:
            scores = {}
            
            # Score each test category
            for test_category, results in self.results.items():
                if "error" in results:
                    scores[test_category] = 0
                    continue
                
                summary_data = results.get("summary", {})
                success_rate = summary_data.get("success_rate", 0)
                scores[test_category] = success_rate * 100
            
            # Calculate overall score
            if scores:
                overall_score = sum(scores.values()) / len(scores)
                readiness["overall_score"] = overall_score
                readiness["category_scores"] = scores
                
                # Determine readiness status
                if overall_score >= 90:
                    readiness["readiness_status"] = "excellent"
                    readiness["production_ready"] = True
                elif overall_score >= 80:
                    readiness["readiness_status"] = "good"
                    readiness["production_ready"] = True
                elif overall_score >= 70:
                    readiness["readiness_status"] = "fair"
                    readiness["production_ready"] = False
                else:
                    readiness["readiness_status"] = "poor"
                    readiness["production_ready"] = False
            
        except Exception as e:
            logger.error(f"Error assessing system readiness: {e}")
            readiness["assessment_error"] = str(e)
        
        return readiness
    
    async def _save_results(self):
        """Save all test results"""
        try:
            # Save individual test results
            for test_name, result in self.results.items():
                filename = f"{self.output_dir}/{test_name}_results.json"
                with open(filename, "w") as f:
                    json.dump(result, f, indent=2, default=str)
            
            # Save comprehensive report
            comprehensive_report = await self._generate_comprehensive_report(0)
            with open(f"{self.output_dir}/comprehensive_integration_report.json", "w") as f:
                json.dump(comprehensive_report, f, indent=2, default=str)
            
            # Generate and save text report
            text_report = self._generate_text_report(comprehensive_report)
            with open(f"{self.output_dir}/integration_report.txt", "w") as f:
                f.write(text_report)
            
            logger.info(f"All integration results saved to {self.output_dir}/")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _generate_text_report(self, comprehensive_report: Dict[str, Any]) -> str:
        """Generate human-readable text report"""
        report = []
        report.append("="*120)
        report.append("COMPREHENSIVE INTEGRATION & ERROR HANDLING TEST SUITE REPORT")
        report.append("="*120)
        
        # Executive Summary
        exec_summary = comprehensive_report.get("executive_summary", {})
        report.append(f"\n🎯 EXECUTIVE SUMMARY")
        report.append("-" * 60)
        report.append(f"Overall Status: {exec_summary.get('overall_status', 'unknown').upper()}")
        report.append(f"System Readiness: {exec_summary.get('system_readiness', 'unknown').upper()}")
        
        # Critical Issues
        critical_issues = exec_summary.get("critical_issues", [])
        if critical_issues:
            report.append(f"\n🚨 CRITICAL ISSUES")
            report.append("-" * 60)
            for issue in critical_issues:
                report.append(f"  ❌ {issue}")
        
        # Integration Highlights
        highlights = exec_summary.get("integration_highlights", [])
        if highlights:
            report.append(f"\n✨ INTEGRATION HIGHLIGHTS")
            report.append("-" * 60)
            for highlight in highlights:
                report.append(f"  ✅ {highlight}")
        
        # Integration Metrics
        metrics = comprehensive_report.get("integration_metrics", {})
        if metrics:
            report.append(f"\n📊 KEY INTEGRATION METRICS")
            report.append("-" * 60)
            for metric, value in metrics.items():
                if isinstance(value, float):
                    report.append(f"  {metric}: {value:.1%}")
                else:
                    report.append(f"  {metric}: {value}")
        
        # System Readiness
        readiness = comprehensive_report.get("system_readiness", {})
        if readiness:
            report.append(f"\n🚀 SYSTEM READINESS ASSESSMENT")
            report.append("-" * 60)
            report.append(f"Overall Score: {readiness.get('overall_score', 0):.1f}/100")
            report.append(f"Readiness Status: {readiness.get('readiness_status', 'unknown').upper()}")
            report.append(f"Production Ready: {'YES' if readiness.get('production_ready', False) else 'NO'}")
        
        # Recommendations
        recommendations = comprehensive_report.get("recommendations", [])
        if recommendations:
            report.append(f"\n💡 COMPREHENSIVE RECOMMENDATIONS")
            report.append("-" * 60)
            for i, rec in enumerate(recommendations, 1):
                report.append(f"  {i:2d}. {rec}")
        
        report.append(f"\n📁 Detailed results available in JSON files")
        report.append("="*120)
        
        return "\n".join(report)


async def main():
    """Main comprehensive integration testing function"""
    try:
        logger.info("🧪 Starting Comprehensive Integration & Error Handling Test Suite")
        
        # Create test suite
        suite = ComprehensiveIntegrationSuite()
        
        # Run complete test suite
        results = await suite.run_complete_integration_suite()
        
        # Print summary
        print("\n" + "="*120)
        print("COMPREHENSIVE INTEGRATION TEST SUITE COMPLETED")
        print("="*120)
        
        exec_summary = results.get("executive_summary", {})
        print(f"\nOverall Status: {exec_summary.get('overall_status', 'unknown').upper()}")
        print(f"System Readiness: {exec_summary.get('system_readiness', 'unknown').upper()}")
        
        readiness = results.get("system_readiness", {})
        print(f"Readiness Score: {readiness.get('overall_score', 0):.1f}/100")
        print(f"Production Ready: {'YES' if readiness.get('production_ready', False) else 'NO'}")
        
        print(f"\n📁 Detailed results saved to: {suite.output_dir}/")
        print("="*120)
        
    except Exception as e:
        logger.error(f"Comprehensive integration testing failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run comprehensive integration testing
    asyncio.run(main())
