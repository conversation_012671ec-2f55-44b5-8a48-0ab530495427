"""
Smart Scrolling Strategy for Facebook Comment Loading
Optimized timing and adaptive delays to trigger API calls efficiently
"""
import asyncio
import random
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add zendriver_local to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'zendriver_local'))

import zendriver as zd
from loguru import logger


class ScrollDirection(Enum):
    DOWN = "down"
    UP = "up"


class ScrollTrigger(Enum):
    TIME_BASED = "time_based"
    API_RESPONSE = "api_response"
    ELEMENT_VISIBLE = "element_visible"
    MANUAL = "manual"


@dataclass
class ScrollAction:
    """Individual scroll action configuration"""
    direction: ScrollDirection
    distance: int
    delay_before: float
    delay_after: float
    trigger: ScrollTrigger
    expected_api_calls: int = 1


@dataclass
class ScrollingConfig:
    """Configuration for smart scrolling strategy"""
    # Basic scrolling parameters
    base_scroll_distance: int = 500
    max_scroll_distance: int = 1000
    min_scroll_distance: int = 200
    
    # Timing parameters
    base_delay: float = 2.0
    min_delay: float = 1.0
    max_delay: float = 5.0
    api_wait_timeout: float = 10.0
    
    # Adaptive parameters
    enable_adaptive_timing: bool = True
    enable_random_variation: bool = True
    random_variation_factor: float = 0.3
    
    # Performance parameters
    max_scroll_attempts: int = 20
    max_comments_target: int = 1000
    scroll_acceleration: bool = True
    
    # Element detection
    load_more_selectors: List[str] = None
    comment_container_selectors: List[str] = None
    
    def __post_init__(self):
        if self.load_more_selectors is None:
            self.load_more_selectors = [
                '[data-testid="UFI2CommentsList/root"] button',
                'div[role="button"]:has-text("View more comments")',
                'div[role="button"]:has-text("Show more comments")',
                'span:has-text("View more comments")',
                'span:has-text("Show more comments")'
            ]
        
        if self.comment_container_selectors is None:
            self.comment_container_selectors = [
                '[data-testid="UFI2CommentsList/root"]',
                '[role="article"]',
                'div[data-pagelet="Comments"]',
                'div[data-testid="comment"]'
            ]


@dataclass
class ScrollingMetrics:
    """Metrics for scrolling performance"""
    total_scrolls: int = 0
    successful_api_triggers: int = 0
    failed_api_triggers: int = 0
    average_api_response_time: float = 0.0
    total_scrolling_time: float = 0.0
    comments_loaded: int = 0
    load_more_clicks: int = 0


class SmartScrollingStrategy:
    """
    Smart scrolling strategy for Facebook comment loading
    Adapts timing and behavior based on API response patterns
    """
    
    def __init__(self, config: ScrollingConfig = None):
        self.config = config or ScrollingConfig()
        self.metrics = ScrollingMetrics()
        
        # State tracking
        self.tab = None
        self.is_scrolling = False
        self.api_response_times: List[float] = []
        self.last_api_call_time: Optional[float] = None
        self.consecutive_failed_scrolls = 0
        
        # Callbacks
        self.api_call_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        self.scroll_callbacks: List[Callable[[ScrollAction], None]] = []
        
    async def initialize(self, tab: zd.Tab) -> bool:
        """Initialize scrolling strategy with browser tab"""
        try:
            self.tab = tab
            logger.info("Smart scrolling strategy initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize scrolling strategy: {e}")
            return False
    
    async def execute_scrolling_sequence(
        self,
        api_monitor_callback: Optional[Callable[[], int]] = None
    ) -> ScrollingMetrics:
        """
        Execute smart scrolling sequence to load comments
        
        Args:
            api_monitor_callback: Function that returns number of new API calls detected
            
        Returns:
            ScrollingMetrics with performance data
        """
        if not self.tab:
            raise ValueError("Scrolling strategy not initialized")
        
        self.is_scrolling = True
        start_time = time.time()
        
        try:
            logger.info("Starting smart scrolling sequence")
            
            # Initial page assessment
            await self._assess_page_state()
            
            # Execute scrolling loop
            scroll_count = 0
            while (scroll_count < self.config.max_scroll_attempts and 
                   self.metrics.comments_loaded < self.config.max_comments_target):
                
                # Calculate next scroll action
                scroll_action = await self._calculate_next_scroll_action(scroll_count)
                
                # Execute scroll action
                success = await self._execute_scroll_action(scroll_action)
                
                if success:
                    # Wait for API response
                    api_calls = await self._wait_for_api_response(api_monitor_callback)
                    
                    if api_calls > 0:
                        self.metrics.successful_api_triggers += 1
                        self.consecutive_failed_scrolls = 0
                    else:
                        self.metrics.failed_api_triggers += 1
                        self.consecutive_failed_scrolls += 1
                    
                    # Try to click load more buttons
                    await self._try_click_load_more()
                    
                    # Adaptive delay based on performance
                    await self._adaptive_delay()
                
                scroll_count += 1
                self.metrics.total_scrolls += 1
                
                # Break if too many consecutive failures
                if self.consecutive_failed_scrolls >= 5:
                    logger.warning("Too many consecutive failed scrolls, stopping")
                    break
            
            self.metrics.total_scrolling_time = time.time() - start_time
            logger.info(f"Scrolling sequence completed: {self.metrics.total_scrolls} scrolls, "
                       f"{self.metrics.successful_api_triggers} API triggers")
            
            return self.metrics
            
        finally:
            self.is_scrolling = False
    
    async def _assess_page_state(self):
        """Assess initial page state and comment structure"""
        try:
            # Check for existing comments
            for selector in self.config.comment_container_selectors:
                try:
                    elements = await self.tab.select_all(selector)
                    if elements:
                        self.metrics.comments_loaded = len(elements)
                        logger.debug(f"Found {len(elements)} initial comments")
                        break
                except:
                    continue
            
            # Check page height for scroll planning
            page_height = await self.tab.evaluate("document.body.scrollHeight")
            viewport_height = await self.tab.evaluate("window.innerHeight")
            
            logger.debug(f"Page height: {page_height}, Viewport: {viewport_height}")
            
        except Exception as e:
            logger.warning(f"Failed to assess page state: {e}")
    
    async def _calculate_next_scroll_action(self, scroll_count: int) -> ScrollAction:
        """Calculate optimal next scroll action based on current state"""
        
        # Base scroll distance with adaptive adjustment
        base_distance = self.config.base_scroll_distance
        
        if self.config.scroll_acceleration and scroll_count > 5:
            # Increase scroll distance for later scrolls
            base_distance = min(
                self.config.max_scroll_distance,
                base_distance + (scroll_count - 5) * 50
            )
        
        # Apply random variation if enabled
        if self.config.enable_random_variation:
            variation = random.uniform(
                -self.config.random_variation_factor,
                self.config.random_variation_factor
            )
            base_distance = int(base_distance * (1 + variation))
        
        # Ensure within bounds
        scroll_distance = max(
            self.config.min_scroll_distance,
            min(self.config.max_scroll_distance, base_distance)
        )
        
        # Calculate delays
        delay_before = self._calculate_adaptive_delay("before")
        delay_after = self._calculate_adaptive_delay("after")
        
        return ScrollAction(
            direction=ScrollDirection.DOWN,
            distance=scroll_distance,
            delay_before=delay_before,
            delay_after=delay_after,
            trigger=ScrollTrigger.TIME_BASED
        )
    
    def _calculate_adaptive_delay(self, timing: str) -> float:
        """Calculate adaptive delay based on API response patterns"""
        base_delay = self.config.base_delay
        
        if self.config.enable_adaptive_timing and self.api_response_times:
            # Adjust delay based on average API response time
            avg_response_time = sum(self.api_response_times[-10:]) / len(self.api_response_times[-10:])
            
            if timing == "after":
                # Wait longer after scroll if APIs are slow
                base_delay = max(self.config.min_delay, avg_response_time * 1.2)
            else:
                # Shorter delay before scroll
                base_delay = max(self.config.min_delay, avg_response_time * 0.5)
        
        # Add random variation
        if self.config.enable_random_variation:
            variation = random.uniform(0.8, 1.2)
            base_delay *= variation
        
        return min(self.config.max_delay, max(self.config.min_delay, base_delay))
    
    async def _execute_scroll_action(self, action: ScrollAction) -> bool:
        """Execute individual scroll action"""
        try:
            # Pre-scroll delay
            if action.delay_before > 0:
                await asyncio.sleep(action.delay_before)
            
            # Execute scroll
            if action.direction == ScrollDirection.DOWN:
                await self.tab.scroll_down(action.distance)
            else:
                await self.tab.scroll_up(action.distance)
            
            # Post-scroll delay
            if action.delay_after > 0:
                await asyncio.sleep(action.delay_after)
            
            # Notify callbacks
            for callback in self.scroll_callbacks:
                try:
                    callback(action)
                except Exception as e:
                    logger.warning(f"Error in scroll callback: {e}")
            
            logger.debug(f"Executed scroll: {action.direction.value} {action.distance}px")
            return True
            
        except Exception as e:
            logger.warning(f"Failed to execute scroll action: {e}")
            return False
    
    async def _wait_for_api_response(
        self,
        api_monitor_callback: Optional[Callable[[], int]] = None
    ) -> int:
        """Wait for API response after scroll"""
        if not api_monitor_callback:
            # Simple time-based wait
            await asyncio.sleep(self.config.base_delay)
            return 1  # Assume success
        
        start_time = time.time()
        initial_count = api_monitor_callback()
        
        # Wait for new API calls
        timeout = self.config.api_wait_timeout
        while time.time() - start_time < timeout:
            await asyncio.sleep(0.5)
            current_count = api_monitor_callback()
            
            if current_count > initial_count:
                response_time = time.time() - start_time
                self.api_response_times.append(response_time)
                self.last_api_call_time = time.time()
                
                # Keep only recent response times
                if len(self.api_response_times) > 20:
                    self.api_response_times = self.api_response_times[-20:]
                
                return current_count - initial_count
        
        return 0  # No new API calls detected
    
    async def _try_click_load_more(self) -> bool:
        """Try to click 'Load more comments' buttons"""
        try:
            for selector in self.config.load_more_selectors:
                try:
                    # Try to find and click load more button
                    button = await self.tab.find("View more comments", best_match=True)
                    if button:
                        await button.click()
                        self.metrics.load_more_clicks += 1
                        await asyncio.sleep(1)  # Wait for click to process
                        logger.debug("Clicked load more comments button")
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Failed to click load more: {e}")
            return False
    
    async def _adaptive_delay(self):
        """Apply adaptive delay based on current performance"""
        if self.consecutive_failed_scrolls > 0:
            # Increase delay after failed scrolls
            delay = self.config.base_delay * (1 + self.consecutive_failed_scrolls * 0.5)
            delay = min(self.config.max_delay, delay)
            await asyncio.sleep(delay)
        else:
            # Normal delay
            await asyncio.sleep(self.config.base_delay)
    
    def add_api_call_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for API call events"""
        self.api_call_callbacks.append(callback)
    
    def add_scroll_callback(self, callback: Callable[[ScrollAction], None]):
        """Add callback for scroll events"""
        self.scroll_callbacks.append(callback)
    
    def get_metrics(self) -> ScrollingMetrics:
        """Get current scrolling metrics"""
        if self.api_response_times:
            self.metrics.average_api_response_time = sum(self.api_response_times) / len(self.api_response_times)
        return self.metrics
    
    def reset_metrics(self):
        """Reset scrolling metrics"""
        self.metrics = ScrollingMetrics()
        self.api_response_times.clear()
        self.consecutive_failed_scrolls = 0


# Factory functions
def create_facebook_scrolling_config(
    performance_mode: bool = False,
    max_comments: int = 1000
) -> ScrollingConfig:
    """Create scrolling configuration optimized for Facebook"""
    
    if performance_mode:
        return ScrollingConfig(
            base_scroll_distance=800,
            base_delay=1.5,
            min_delay=0.8,
            max_delay=3.0,
            max_scroll_attempts=15,
            max_comments_target=max_comments,
            scroll_acceleration=True,
            enable_adaptive_timing=True,
            random_variation_factor=0.2
        )
    else:
        return ScrollingConfig(
            base_scroll_distance=500,
            base_delay=2.0,
            min_delay=1.0,
            max_delay=4.0,
            max_scroll_attempts=20,
            max_comments_target=max_comments,
            scroll_acceleration=False,
            enable_adaptive_timing=True,
            random_variation_factor=0.3
        )


def create_smart_scroller(
    config: ScrollingConfig = None
) -> SmartScrollingStrategy:
    """Create smart scrolling strategy for Facebook comment loading"""
    
    if config is None:
        config = create_facebook_scrolling_config()
    
    return SmartScrollingStrategy(config)
