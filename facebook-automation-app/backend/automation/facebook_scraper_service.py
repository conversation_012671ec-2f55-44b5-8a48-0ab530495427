"""
Integrated Facebook Scraper Service with API Interception
Uses Zendriver with CDP Network Monitoring for optimal Facebook comment extraction
Replaces HTML parsing with direct API response interception
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Callable
from loguru import logger
from pathlib import Path

# New API interception components
from automation.facebook_comment_interceptor import <PERSON>CommentAPIInter<PERSON>, InterceptionConfig, create_facebook_comment_interceptor
from automation.stealth_browser_manager import StealthBrowserManager, AntidetectProfile, ProxyConfig, create_facebook_profile, create_stealth_config
from automation.network_monitor import NetworkMonitor, create_facebook_network_monitor
from automation.smart_scrolling import SmartScrollingStrategy, create_smart_scroller, create_facebook_scrolling_config
from automation.facebook_data_parser import FacebookDataParser, create_facebook_parser

# Legacy components (kept for compatibility) - use try/except for optional imports
try:
    from deduplication_system import UIDDeduplicationSystem
except ImportError:
    # Create a simple fallback deduplication system
    class UIDDeduplicationSystem:
        def __init__(self, max_memory_uids=50000, persistence_file=None):
            self.seen_uids = set()
            self.persistence_file = persistence_file

        def add_uids_batch(self, uids):
            new_uids = [uid for uid in uids if uid not in self.seen_uids]
            duplicate_uids = [uid for uid in uids if uid in self.seen_uids]
            self.seen_uids.update(new_uids)
            return new_uids, duplicate_uids

        def get_statistics(self):
            return {"total_unique_uids": len(self.seen_uids)}

        def save_to_file(self):
            pass

try:
    from error_handler import RobustErrorHandler, RetryConfig, ErrorType
except ImportError:
    # Create simple fallback error handler
    class RetryConfig:
        def __init__(self, max_attempts=3, base_delay=2.0, max_delay=120.0, backoff_multiplier=2.0, jitter=True):
            self.max_attempts = max_attempts
            self.base_delay = base_delay
            self.max_delay = max_delay
            self.backoff_multiplier = backoff_multiplier
            self.jitter = jitter

    class RobustErrorHandler:
        def __init__(self, retry_config):
            self.retry_config = retry_config

        async def execute_with_retry(self, func, args, kwargs, recovery_enabled=True):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in function execution: {e}")
                return {"success": False, "error": str(e)}

        def get_error_statistics(self):
            return {"total_errors": 0, "retry_attempts": 0}

try:
    from performance_monitor import PerformanceMonitor
except ImportError:
    # Create simple fallback performance monitor
    class PerformanceMonitor:
        def __init__(self, collection_interval=30.0):
            self.collection_interval = collection_interval

        async def start_monitoring(self):
            pass

        async def stop_monitoring(self):
            pass

        def add_anomaly_callback(self, callback):
            pass

        def get_current_metrics(self):
            return {"cpu_usage": 0, "memory_usage": 0}


class FacebookScraperService:
    """
    Complete Facebook scraping service with API interception
    Uses Zendriver + CDP Network Monitoring for optimal performance
    """

    def __init__(self, persistence_dir: str = "scraping_data"):
        # Initialize new API interception components
        self.stealth_browser_manager = StealthBrowserManager()
        self.network_monitor = create_facebook_network_monitor()
        self.data_parser = create_facebook_parser()

        # Active interceptors by profile
        self.active_interceptors: Dict[str, FacebookCommentAPIInterceptor] = {}

        # Setup persistence
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(exist_ok=True)

        # Initialize deduplication system with persistence
        dedup_file = self.persistence_dir / "uid_deduplication.json"
        self.deduplication_system = UIDDeduplicationSystem(
            max_memory_uids=50000,
            persistence_file=str(dedup_file)
        )

        # Initialize error handler with custom retry configuration
        retry_config = RetryConfig(
            max_attempts=3,
            base_delay=2.0,
            max_delay=120.0,
            backoff_multiplier=2.0,
            jitter=True
        )
        self.error_handler = RobustErrorHandler(retry_config)

        # Initialize performance monitor
        self.performance_monitor = PerformanceMonitor(collection_interval=30.0)

        # Setup anomaly detection callback
        self.performance_monitor.add_anomaly_callback(self._handle_performance_anomaly)

        # Performance monitoring will be started when needed
        self._monitoring_started = False

        # Scraping statistics
        self.scraping_stats = {
            "sessions_created": 0,
            "pages_scraped": 0,
            "total_uids_extracted": 0,
            "unique_uids_found": 0,
            "total_scraping_time": 0,
            "api_calls_intercepted": 0,
            "start_time": None
        }

    async def _ensure_monitoring_started(self):
        """Ensure performance monitoring is started"""
        if not self._monitoring_started:
            await self.performance_monitor.start_monitoring()
            self._monitoring_started = True

    async def scrape_facebook_post_uids(
        self,
        profile_config: Dict[str, Any],
        post_url: str,
        max_comments: int = 1000,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Complete workflow to scrape UIDs from Facebook post using API interception

        Args:
            profile_config: Antidetect profile configuration
            post_url: Facebook post URL to scrape
            max_comments: Maximum number of comments to extract
            progress_callback: Function to report progress
        """
        return await self.error_handler.execute_with_retry(
            self._scrape_facebook_post_uids_with_api_interception,
            (profile_config, post_url, max_comments, progress_callback),
            {},
            recovery_enabled=True
        )

    async def _scrape_facebook_post_uids_with_api_interception(
        self,
        profile_config: Dict[str, Any],
        post_url: str,
        max_comments: int = 1000,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Internal implementation using API interception approach

        Args:
            profile_config: Antidetect profile configuration
            post_url: Facebook post URL to scrape
            max_comments: Maximum number of comments to extract
            progress_callback: Function to report progress
        """
        # Ensure monitoring is started
        await self._ensure_monitoring_started()

        start_time = time.time()
        interceptor = None

        try:
            logger.info(f"Starting Facebook API interception for post: {post_url}")

            # Step 1: Get actual profile from database and create proper browser config
            profile_id = profile_config.get('profile_id', 'default')
            actual_profile_path = await self._get_profile_browser_path(profile_id)

            # Update profile config with actual browser path
            updated_profile_config = profile_config.copy()
            updated_profile_config['user_data_dir'] = actual_profile_path

            logger.info(f"Using profile browser path: {actual_profile_path}")

            # Step 2: Create antidetect profile with updated config
            profile = self._create_antidetect_profile(updated_profile_config)

            # Step 3: Create and initialize interceptor
            interceptor = await self._create_comment_interceptor(profile, max_comments)

            # Step 3: Extract comments using API interception with modal support
            logger.info("Extracting comments via API interception with advanced modal scrolling...")
            extraction_result = await interceptor.extract_comments(post_url)

            # Step 4: Parse and extract UIDs from comments
            logger.info("Parsing comment data...")
            all_uids = set()
            all_users = []

            for comment in extraction_result.comments:
                # Add comment author UID
                if comment.uid and comment.uid.isdigit():
                    all_uids.add(comment.uid)
                    all_users.append({
                        'uid': comment.uid,
                        'username': comment.username,
                        'profile_url': comment.profile_url,
                        'is_anonymous': comment.is_anonymous
                    })

            # Step 5: Deduplicate UIDs
            logger.info("Deduplicating UIDs...")
            new_uids, duplicate_uids = self.deduplication_system.add_uids_batch(list(all_uids))

            # Step 6: Save results
            await self._save_api_scraping_results(profile_config, post_url, new_uids, {
                "extraction_method": "api_interception",
                "total_comments": extraction_result.total_extracted,
                "processing_time": extraction_result.processing_time,
                "api_calls_intercepted": extraction_result.metadata.get('api_calls_intercepted', 0),
                "total_uids": len(all_uids),
                "new_uids": len(new_uids),
                "duplicates": len(duplicate_uids)
            })

            # Update statistics
            scraping_time = time.time() - start_time
            self.scraping_stats["pages_scraped"] += 1
            self.scraping_stats["total_uids_extracted"] += len(all_uids)
            self.scraping_stats["unique_uids_found"] += len(new_uids)
            self.scraping_stats["total_scraping_time"] += scraping_time
            self.scraping_stats["api_calls_intercepted"] += extraction_result.metadata.get('api_calls_intercepted', 0)

            logger.info(f"API interception completed: {len(new_uids)} new UIDs from {extraction_result.total_extracted} comments in {scraping_time:.1f}s")

            return {
                "success": True,
                "results": {
                    "post_url": post_url,
                    "extraction_method": "api_interception",
                    "total_comments_extracted": extraction_result.total_extracted,
                    "total_uids_found": len(all_uids),
                    "new_uids": new_uids,
                    "duplicate_count": len(duplicate_uids),
                    "scraping_time": scraping_time,
                    "api_interception_stats": {
                        "api_calls_intercepted": extraction_result.metadata.get('api_calls_intercepted', 0),
                        "scroll_attempts": extraction_result.metadata.get('scroll_attempts', 0),
                        "processing_time": extraction_result.processing_time
                    },
                    "user_details": all_users[:100],  # Limit to first 100 for response size
                    "deduplication_stats": self.deduplication_system.get_statistics()
                }
            }

        except Exception as e:
            logger.error(f"Error in API interception scraping: {e}")
            # Calculate scraping time safely
            scraping_time = time.time() - start_time if 'start_time' in locals() else 0
            return {
                "success": False,
                "error": str(e),
                "scraping_time": scraping_time
            }

        finally:
            # Cleanup interceptor
            if interceptor:
                try:
                    await interceptor.cleanup()
                except Exception as e:
                    logger.warning(f"Error cleaning up interceptor: {e}")

    async def _get_profile_browser_path(self, profile_id: str) -> str:
        """Get the actual browser profile path using browser service"""
        try:
            # Import here to avoid circular imports
            from app.services.browser_service import BrowserService
            from app.models.profile import Profile
            from app.core.database import AsyncSessionLocal
            from sqlalchemy import select

            # Get profile from database
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(Profile).where(Profile.id == profile_id))
                profile = result.scalar_one_or_none()

                if profile:
                    # Use browser service to get proper profile path
                    browser_service = BrowserService()
                    browser_config = browser_service.create_browser_config(profile)
                    profile_path = browser_config["profile_path"]

                    logger.info(f"Found profile {profile_id} with browser path: {profile_path}")
                    return profile_path
                else:
                    # Fallback to default path
                    fallback_path = f"./browser_profiles/{profile_id}"
                    logger.warning(f"Profile {profile_id} not found in database, using fallback: {fallback_path}")
                    return fallback_path

        except Exception as e:
            # Fallback to default path
            fallback_path = f"./browser_profiles/{profile_id}"
            logger.warning(f"Error getting profile browser path: {e}, using fallback: {fallback_path}")
            return fallback_path

    def _create_antidetect_profile(self, profile_config: Dict[str, Any]) -> AntidetectProfile:
        """Create antidetect profile from configuration"""

        # Extract proxy configuration
        proxy_config = ProxyConfig(proxy_type='none')
        if 'proxy' in profile_config and profile_config['proxy']:
            proxy_data = profile_config['proxy']
            proxy_config = ProxyConfig(
                proxy_type=proxy_data.get('type', 'http'),
                host=proxy_data.get('host'),
                port=proxy_data.get('port'),
                username=proxy_data.get('username'),
                password=proxy_data.get('password')
            )

        # Create profile
        return create_facebook_profile(
            profile_id=profile_config.get('profile_id', 'default'),
            profile_name=profile_config.get('profile_name', 'Facebook Scraper'),
            user_data_dir=profile_config.get('user_data_dir', './browser_profiles/default'),
            proxy_config=proxy_config
        )

    async def _create_comment_interceptor(
        self,
        profile: AntidetectProfile,
        max_comments: int
    ) -> FacebookCommentAPIInterceptor:
        """Create and initialize comment interceptor"""

        # Create interceptor configuration
        config = InterceptionConfig(
            headless=False,  # Visible browser with off-screen positioning
            max_comments=max_comments,
            off_screen_position=(-2000, -2000),
            window_size=(1920, 1080)
        )

        # Create profile config dict for interceptor
        profile_config = {
            'user_data_dir': profile.user_data_dir,
            'proxy': {
                'host': profile.proxy_config.host,
                'port': profile.proxy_config.port,
                'username': profile.proxy_config.username,
                'password': profile.proxy_config.password
            } if profile.proxy_config.proxy_type != 'none' else None
        }

        # Create and initialize interceptor
        interceptor = FacebookCommentAPIInterceptor(config, profile_config)

        if not await interceptor.initialize():
            raise Exception("Failed to initialize Facebook comment interceptor")

        # Store active interceptor
        self.active_interceptors[profile.profile_id] = interceptor

        return interceptor

    async def _save_api_scraping_results(
        self,
        profile_config: Dict[str, Any],
        post_url: str,
        uids: List[str],
        metadata: Dict[str, Any]
    ):
        """Save API scraping results to persistence"""
        try:
            results_file = self.persistence_dir / f"api_scraping_results_{int(time.time())}.json"

            results_data = {
                "timestamp": time.time(),
                "profile_id": profile_config.get('profile_id', 'unknown'),
                "post_url": post_url,
                "extraction_method": "api_interception",
                "uids": uids,
                "metadata": metadata,
                "scraping_stats": self.scraping_stats.copy()
            }

            import json
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)

            logger.debug(f"Saved API scraping results to {results_file}")

        except Exception as e:
            logger.warning(f"Failed to save API scraping results: {e}")
    
    async def scrape_multiple_posts(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape multiple Facebook posts in sequence"""
        try:
            results = []
            total_new_uids = []
            
            for i, post_url in enumerate(post_urls):
                logger.info(f"Scraping post {i+1}/{len(post_urls)}: {post_url}")
                
                result = await self.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_url,
                    progress_callback=progress_callback
                )
                
                results.append(result)
                
                if result["success"]:
                    total_new_uids.extend(result["results"]["new_uids"])
                
                # Small delay between posts
                await asyncio.sleep(2)
            
            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": len([r for r in results if r["success"]]),
                    "total_unique_uids": len(set(total_new_uids)),
                    "individual_results": results,
                    "overall_stats": self.get_scraping_statistics()
                }
            }
            
        except Exception as e:
            logger.error(f"Error scraping multiple posts: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_multiple_posts_parallel(
        self,
        profile_id: str,
        post_urls: List[str],
        max_concurrent: Optional[int] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape multiple Facebook posts in parallel for maximum performance"""
        try:
            logger.info(f"Starting parallel scraping for {len(post_urls)} posts")

            # Create processing tasks
            tasks = []
            for i, post_url in enumerate(post_urls):
                task = ProcessingTask(
                    task_id=f"parallel_task_{int(time.time())}_{i}",
                    profile_id=profile_id,
                    post_url=post_url,
                    priority=1,
                    max_scroll_time=300
                )
                tasks.append(task)

            # Submit tasks to parallel processor
            task_ids = await self.parallel_processor.submit_multiple_tasks(tasks)

            # Process tasks in parallel
            results = await self.parallel_processor.process_tasks_parallel(
                max_concurrent=max_concurrent,
                progress_callback=progress_callback
            )

            # Aggregate results
            total_new_uids = []
            successful_scrapes = 0
            failed_scrapes = 0

            for result in results:
                if result.success:
                    successful_scrapes += 1
                    if result.uids:
                        total_new_uids.extend(result.uids)
                else:
                    failed_scrapes += 1

            # Remove duplicates across all results
            unique_uids = list(set(total_new_uids))

            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": successful_scrapes,
                    "failed_scrapes": failed_scrapes,
                    "total_unique_uids": len(unique_uids),
                    "all_unique_uids": unique_uids,
                    "individual_results": [
                        {
                            "task_id": r.task_id,
                            "success": r.success,
                            "uids_count": len(r.uids) if r.uids else 0,
                            "processing_time": r.processing_time,
                            "error": r.error
                        } for r in results
                    ],
                    "parallel_processing_stats": self.parallel_processor.get_processing_statistics(),
                    "overall_stats": self.get_scraping_statistics()
                }
            }

        except Exception as e:
            logger.error(f"Error in parallel scraping: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_with_auto_optimization(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Automatically optimize scraping strategy based on system resources and post count"""
        try:
            post_count = len(post_urls)

            # Auto-scale workers based on current system resources
            await self.parallel_processor.auto_scale_workers()

            # Choose optimal processing strategy
            if post_count == 1:
                # Single post - use regular scraping
                return await self.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_urls[0],
                    progress_callback=progress_callback
                )
            elif post_count <= 5:
                # Small batch - use concurrent processing
                return await self.scrape_multiple_posts(
                    profile_id=profile_id,
                    post_urls=post_urls,
                    progress_callback=progress_callback
                )
            else:
                # Large batch - use parallel processing
                optimal_concurrency = self.parallel_processor.resource_monitor.get_optimal_concurrency()
                return await self.scrape_multiple_posts_parallel(
                    profile_id=profile_id,
                    post_urls=post_urls,
                    max_concurrent=optimal_concurrency,
                    progress_callback=progress_callback
                )

        except Exception as e:
            logger.error(f"Error in auto-optimized scraping: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_with_memory_optimization(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape with advanced memory optimization and streaming processing"""
        try:
            async with self.memory_optimizer.memory_managed_operation(
                f"scrape_memory_optimized_{len(post_urls)}_posts"
            ):
                logger.info(f"Starting memory-optimized scraping for {len(post_urls)} posts")

                # Use streaming processor for large datasets
                if len(post_urls) > 10:
                    return await self._stream_process_posts(
                        profile_id, post_urls, progress_callback
                    )
                else:
                    # Use regular parallel processing for smaller datasets
                    return await self.scrape_with_auto_optimization(
                        profile_id, post_urls, progress_callback
                    )

        except Exception as e:
            logger.error(f"Error in memory-optimized scraping: {e}")
            return {"success": False, "error": str(e)}

    async def _stream_process_posts(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Stream process posts for memory efficiency"""
        try:
            all_uids = []
            processed_posts = 0
            successful_scrapes = 0
            failed_scrapes = 0

            async def uid_generator():
                """Generate UIDs from posts in streaming fashion"""
                for post_url in post_urls:
                    try:
                        result = await self._scrape_facebook_post_uids_internal(
                            profile_id, post_url, 300, None
                        )

                        if result["success"] and result["results"]["new_uids"]:
                            for uid in result["results"]["new_uids"]:
                                yield uid

                        nonlocal processed_posts, successful_scrapes, failed_scrapes
                        processed_posts += 1
                        if result["success"]:
                            successful_scrapes += 1
                        else:
                            failed_scrapes += 1

                        if progress_callback:
                            await progress_callback({
                                "processed_posts": processed_posts,
                                "total_posts": len(post_urls),
                                "successful_scrapes": successful_scrapes,
                                "failed_scrapes": failed_scrapes
                            })

                    except Exception as e:
                        logger.error(f"Error processing post {post_url}: {e}")
                        failed_scrapes += 1

            async def process_uid_chunk(uid_chunk: List[str]) -> List[str]:
                """Process chunk of UIDs with deduplication"""
                return self.deduplication_system.add_uids_batch(uid_chunk)

            # Stream process UIDs
            async for processed_chunk in self.streaming_processor.process_uids_stream(
                uid_generator(), process_uid_chunk, progress_callback
            ):
                all_uids.extend(processed_chunk)

            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": successful_scrapes,
                    "failed_scrapes": failed_scrapes,
                    "total_unique_uids": len(all_uids),
                    "all_unique_uids": all_uids,
                    "memory_stats": self.memory_optimizer.get_memory_statistics(),
                    "streaming_stats": {
                        "processed_count": self.streaming_processor.processed_count,
                        "chunk_size": self.streaming_processor.chunk_size
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error in stream processing: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_performance_anomaly(self, anomalies: List[Dict[str, Any]]):
        """Handle detected performance anomalies"""
        try:
            for anomaly in anomalies:
                logger.warning(f"Performance anomaly detected: {anomaly}")

                # Take corrective actions based on anomaly type
                if anomaly["type"] == "high_memory":
                    await self.memory_optimizer.optimize_memory_usage()
                elif anomaly["type"] == "high_cpu":
                    # Reduce concurrent operations
                    if hasattr(self, 'parallel_processor'):
                        await self.parallel_processor.auto_scale_workers()
                elif anomaly["type"] == "high_error_rate":
                    # Log error rate issue
                    logger.error(f"High error rate detected for operation: {anomaly.get('operation')}")
                elif anomaly["type"] == "slow_response":
                    # Log slow response
                    logger.warning(f"Slow response detected for: {anomaly.get('metric')}")

        except Exception as e:
            logger.error(f"Error handling performance anomaly: {e}")

    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        try:
            return self.performance_monitor.get_comprehensive_report()
        except Exception as e:
            logger.error(f"Error getting performance report: {e}")
            return {"error": str(e)}

    async def _ensure_facebook_session(self, profile_id: str) -> Dict[str, Any]:
        """Ensure Facebook session is active with proper profile validation"""
        try:
            logger.info(f"Ensuring Facebook session for profile: {profile_id}")

            # First, validate that the profile has Facebook login information
            profile_validation = await self._validate_profile_for_scraping(profile_id)
            if not profile_validation["success"]:
                return profile_validation

            # Check if session already exists and is active
            if await self.session_handler.is_session_active(profile_id):
                logger.info(f"Using existing Facebook session for profile: {profile_id}")

                # Skip login verification for now - let scraper handle login issues
                logger.info("Skipping login verification - proceeding with existing session")
                return {"success": True, "message": "Session already active - proceeding without verification"}

            # Initiate new login with saved cookies
            login_result = await self.session_handler.initiate_login_with_cookies(profile_id)
            if not login_result["success"]:
                logger.error(f"Failed to initiate login with cookies: {login_result.get('error', 'Unknown error')}")
                return login_result

            # Wait for login completion or verification
            logger.info("Verifying Facebook login status...")
            max_wait = 60  # 1 minute for cookie-based login
            wait_time = 0

            while wait_time < max_wait:
                status = await self.session_handler.check_login_status(profile_id)

                if status["success"] and status["status"] == "logged_in":
                    complete_result = await self.session_handler.complete_login(profile_id)
                    self.scraping_stats["sessions_created"] += 1
                    logger.info(f"Facebook session established successfully for profile: {profile_id}")
                    return complete_result
                elif status["success"] and status["status"] == "login_required":
                    return {
                        "success": False,
                        "error": "Profile not logged in to Facebook. Please login first using the 'Facebook Login' button.",
                        "status": "login_required"
                    }

                await asyncio.sleep(3)
                wait_time += 3

            return {
                "success": False,
                "error": "Login verification timeout. Profile may not be properly logged in to Facebook.",
                "status": "timeout"
            }

        except Exception as e:
            logger.error(f"Error ensuring Facebook session: {e}")
            return {"success": False, "error": str(e)}

    async def _validate_profile_for_scraping(self, profile_id: str) -> Dict[str, Any]:
        """Validate that profile has necessary Facebook login information for scraping"""
        try:
            # This would typically check the database for profile information
            # For now, we'll assume the profile exists and check for cookies/session data
            logger.info(f"Validating profile {profile_id} for scraping")

            # Check if profile directory exists and has cookies
            from pathlib import Path

            # Try multiple possible profile directory locations
            possible_dirs = [
                Path(f"browser_profiles/{profile_id}"),
                Path(f"browser_sessions/profile_{profile_id}"),
                Path(f"browser_sessions/{profile_id}")
            ]

            profile_dir = None
            for dir_path in possible_dirs:
                if dir_path.exists():
                    profile_dir = dir_path
                    break

            if not profile_dir:
                return {
                    "success": False,
                    "error": f"Profile directory not found: {profile_id}. Please create the profile first.",
                    "status": "profile_not_found"
                }

            # Check for cookies in different possible locations
            cookies_locations = [
                profile_dir / "Default" / "Cookies",
                profile_dir / "Cookies",
                profile_dir / "Default" / "Network" / "Cookies"
            ]

            cookies_found = False
            for cookies_file in cookies_locations:
                if cookies_file.exists():
                    cookies_found = True
                    break

            if not cookies_found:
                logger.warning(f"No browser cookies found for profile: {profile_id}. Profile may need Facebook login.")
                # Don't fail validation - let the scraper attempt to work with the profile
                # return {
                #     "success": False,
                #     "error": f"No browser cookies found for profile: {profile_id}. Please login to Facebook first.",
                #     "status": "no_cookies"
                # }

            logger.info(f"Profile {profile_id} validation passed - using directory: {profile_dir}")
            return {"success": True, "message": "Profile validated for scraping", "profile_dir": str(profile_dir)}

        except Exception as e:
            logger.error(f"Error validating profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}

    async def _verify_facebook_login_status(self, profile_id: str) -> Dict[str, Any]:
        """Verify if the current session is actually logged in to Facebook"""
        try:
            browser = await self.browser_manager.get_browser(profile_id)
            if not browser:
                return {"success": False, "logged_in": False, "message": "Browser not available"}

            # Navigate to Facebook and check login status
            page = browser.main_tab
            await page.get("https://www.facebook.com")
            await asyncio.sleep(3)

            # Check current URL and page content for login indicators
            current_url = await page.evaluate("window.location.href")
            page_title = await page.evaluate("document.title")

            # Simple check - if redirected to login page, not logged in
            if "login" in current_url.lower() or "login" in page_title.lower():
                return {
                    "success": True,
                    "logged_in": False,
                    "message": "Redirected to login page - not logged in"
                }

            # Additional check for Facebook-specific elements that indicate login
            try:
                # Look for user menu or profile elements
                user_menu = await page.evaluate("""
                    document.querySelector('[data-testid="blue_bar_profile_link"]') ||
                    document.querySelector('[aria-label*="Account"]') ||
                    document.querySelector('[role="button"][aria-label*="Account"]')
                """)

                if user_menu:
                    return {
                        "success": True,
                        "logged_in": True,
                        "message": "Successfully logged in to Facebook"
                    }
            except Exception as e:
                logger.warning(f"Error checking for user menu elements: {e}")

            return {
                "success": True,
                "logged_in": False,
                "message": "Could not verify login status"
            }

        except Exception as e:
            logger.error(f"Error verifying Facebook login status: {e}")
            return {"success": False, "logged_in": False, "message": str(e)}
    
    async def _extract_all_uids_from_page(self, browser) -> Set[str]:
        """Extract all UIDs from current page"""
        try:
            page = browser.main_tab
            if not page:
                return set()
            
            all_uids = set()
            
            # Extract from page source
            current_url = page.url
            page_uids = await self.uid_extractor.extract_uids_from_page(browser, current_url)
            all_uids.update(page_uids)
            
            # Extract specifically from comments section
            comment_uids = await self.uid_extractor.extract_uids_from_comments_section(page)
            all_uids.update(comment_uids)
            
            # Filter valid UIDs
            valid_uids = self.uid_extractor.filter_valid_uids(all_uids)
            
            logger.info(f"Extracted {len(valid_uids)} valid UIDs from page")
            return valid_uids
            
        except Exception as e:
            logger.error(f"Error extracting UIDs from page: {e}")
            return set()
    
    async def _save_scraping_results(
        self, 
        profile_id: str, 
        post_url: str, 
        uids: List[str], 
        metadata: Dict[str, Any]
    ):
        """Save scraping results to file"""
        try:
            # Create results file
            timestamp = int(time.time())
            filename = f"scraping_results_{profile_id}_{timestamp}.json"
            results_file = self.persistence_dir / filename
            
            data = {
                "profile_id": profile_id,
                "post_url": post_url,
                "timestamp": timestamp,
                "uids": uids,
                "metadata": metadata,
                "scraping_stats": self.scraping_stats
            }
            
            import json
            with open(results_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            # Save deduplication state
            self.deduplication_system.save_to_file()
            
            logger.info(f"Results saved to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _create_dynamic_progress_callback(self, main_callback: Optional[Callable]) -> Callable:
        """Create progress callback for dynamic loading"""
        async def callback(stats):
            if main_callback:
                await main_callback({
                    "stage": "dynamic_loading",
                    "stats": stats
                })
        return callback
    
    def get_scraping_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        stats = {
            "scraping_stats": self.scraping_stats,
            "deduplication_stats": self.deduplication_system.get_statistics(),
            "api_interception_stats": {
                "active_interceptors": len(self.active_interceptors),
                "interceptor_profiles": list(self.active_interceptors.keys())
            },
            "browser_stats": {
                "active_stealth_browsers": len(self.stealth_browser_manager.get_active_profiles())
            },
            "error_stats": self.error_handler.get_error_statistics()
        }

        # Add legacy stats if components exist
        if hasattr(self, 'browser_manager'):
            stats["legacy_browser_stats"] = {
                "active_browsers": len(getattr(self.browser_manager, 'browsers', {})),
                "active_sessions": len(getattr(self, 'session_handler', {}).get('sessions', {}))
            }

        if hasattr(self, 'parallel_processor'):
            stats["parallel_processing_stats"] = self.parallel_processor.get_processing_statistics()

        if hasattr(self, 'memory_optimizer'):
            stats["memory_stats"] = self.memory_optimizer.get_memory_statistics()

        if hasattr(self, 'performance_monitor') and self._monitoring_started:
            stats["performance_report"] = self.performance_monitor.get_current_metrics()

        return stats
    
    async def cleanup(self):
        """Cleanup all resources"""
        try:
            # Cleanup all interceptors (new API interception approach)
            await self.cleanup_all_interceptors()

            # Cleanup stealth browser manager
            await self.stealth_browser_manager.close_all_browsers()

            # Stop performance monitoring
            if self._monitoring_started:
                await self.performance_monitor.stop_monitoring()

            # Cleanup legacy components if they exist
            if hasattr(self, 'parallel_processor'):
                await self.parallel_processor.cleanup()

            if hasattr(self, 'memory_optimizer'):
                await self.memory_optimizer.cleanup()

            if hasattr(self, 'browser_manager'):
                await self.browser_manager.close_all_browsers()

            # Save final deduplication state
            self.deduplication_system.save_to_file()

            logger.info("Facebook scraper service cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def get_unique_uids_count(self) -> int:
        """Get total count of unique UIDs collected"""
        return len(self.deduplication_system.get_unique_uids())
    
    async def export_all_uids(self, filename: Optional[str] = None) -> str:
        """Export all unique UIDs to file"""
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"all_unique_uids_{timestamp}.txt"
            
            export_file = self.persistence_dir / filename
            unique_uids = self.deduplication_system.get_unique_uids()
            
            with open(export_file, 'w') as f:
                for uid in sorted(unique_uids):
                    f.write(f"{uid}\n")
            
            logger.info(f"Exported {len(unique_uids)} unique UIDs to {export_file}")
            return str(export_file)
            
        except Exception as e:
            logger.error(f"Error exporting UIDs: {e}")
            return ""

    # New methods for API interception approach
    async def cleanup_interceptor(self, profile_id: str) -> bool:
        """Cleanup specific interceptor"""
        try:
            if profile_id in self.active_interceptors:
                interceptor = self.active_interceptors[profile_id]
                await interceptor.cleanup()
                del self.active_interceptors[profile_id]
                logger.info(f"Cleaned up interceptor for profile: {profile_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error cleaning up interceptor for {profile_id}: {e}")
            return False

    async def cleanup_all_interceptors(self):
        """Cleanup all active interceptors"""
        for profile_id in list(self.active_interceptors.keys()):
            await self.cleanup_interceptor(profile_id)

    def get_active_interceptors(self) -> List[str]:
        """Get list of active interceptor profile IDs"""
        return list(self.active_interceptors.keys())

    async def get_interceptor_status(self, profile_id: str) -> Dict[str, Any]:
        """Get status of specific interceptor"""
        if profile_id not in self.active_interceptors:
            return {"active": False, "error": "Interceptor not found"}

        try:
            interceptor = self.active_interceptors[profile_id]
            # Add status check logic here if needed
            return {
                "active": True,
                "profile_id": profile_id,
                "browser_healthy": True  # Could add actual health check
            }
        except Exception as e:
            return {"active": False, "error": str(e)}


# Factory function for easy instantiation
async def create_facebook_scraper_service(
    persistence_dir: str = "scraping_data"
) -> FacebookScraperService:
    """
    Create and initialize Facebook scraper service with API interception

    Args:
        persistence_dir: Directory for storing scraping data

    Returns:
        Initialized FacebookScraperService instance
    """
    service = FacebookScraperService(persistence_dir)

    # Initialize performance monitoring
    await service._ensure_monitoring_started()

    logger.info("Facebook scraper service with API interception initialized")
    return service


# Example usage with new API interception approach
async def example_api_interception_usage():
    """Example of how to use the new API interception approach"""

    # Profile configuration
    profile_config = {
        'profile_id': 'facebook_scraper_001',
        'profile_name': 'Facebook Comment Scraper',
        'user_data_dir': './browser_profiles/facebook_scraper_001',
        'proxy': {
            'type': 'http',
            'host': '127.0.0.1',
            'port': 8080,
            'username': 'proxy_user',
            'password': 'proxy_pass'
        }
    }

    try:
        # Create scraper service
        scraper = await create_facebook_scraper_service()

        # Scrape Facebook post using API interception
        post_url = "https://www.facebook.com/groups/example/posts/123456789/"
        result = await scraper.scrape_facebook_post_uids(
            profile_config=profile_config,
            post_url=post_url,
            max_comments=500
        )

        if result["success"]:
            results = result["results"]
            print(f"Successfully extracted {results['total_uids_found']} UIDs")
            print(f"New UIDs: {len(results['new_uids'])}")
            print(f"API calls intercepted: {results['api_interception_stats']['api_calls_intercepted']}")
            print(f"Processing time: {results['scraping_time']:.2f}s")
        else:
            print(f"Scraping failed: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Error in example usage: {e}")

    finally:
        # Cleanup
        if 'scraper' in locals():
            await scraper.cleanup()


if __name__ == "__main__":
    # Run example
    asyncio.run(example_api_interception_usage())
