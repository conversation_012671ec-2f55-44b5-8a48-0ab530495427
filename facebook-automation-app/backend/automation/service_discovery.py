"""
ServiceDiscovery - Automatic service discovery and registration
"""
import asyncio
import socket
import time
import json
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, asdict
from loguru import logger

from .service_communication import ServiceEndpoint


@dataclass
class ServiceAnnouncement:
    """Service announcement for discovery"""
    service_name: str
    host: str
    port: int
    protocol: str
    capabilities: List[str]
    version: str
    timestamp: float
    health_endpoint: str = "/health"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServiceAnnouncement':
        return cls(**data)


class ServiceDiscovery:
    """Automatic service discovery mechanism"""
    
    def __init__(self, discovery_port: int = 8090):
        self.discovery_port = discovery_port
        self.announced_services: Dict[str, ServiceAnnouncement] = {}
        self.discovered_services: Dict[str, ServiceAnnouncement] = {}
        self.discovery_callbacks: List[callable] = []
        
        # Discovery settings
        self.announcement_interval = 30.0  # seconds
        self.service_timeout = 90.0  # seconds
        self.discovery_enabled = False
        
        # Tasks
        self.announcement_task: Optional[asyncio.Task] = None
        self.listener_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Network
        self.broadcast_socket: Optional[socket.socket] = None
        self.listen_socket: Optional[socket.socket] = None
    
    async def start_discovery(self) -> bool:
        """Start service discovery"""
        try:
            logger.info("Starting service discovery...")
            
            # Setup UDP sockets
            if not await self._setup_sockets():
                return False
            
            # Start tasks
            self.listener_task = asyncio.create_task(self._discovery_listener())
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_services())
            
            self.discovery_enabled = True
            logger.info(f"Service discovery started on port {self.discovery_port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start service discovery: {e}")
            return False
    
    async def announce_service(
        self,
        service_name: str,
        host: str,
        port: int,
        protocol: str = "http",
        capabilities: List[str] = None,
        version: str = "1.0.0"
    ) -> bool:
        """Announce a service for discovery"""
        try:
            capabilities = capabilities or []
            
            announcement = ServiceAnnouncement(
                service_name=service_name,
                host=host,
                port=port,
                protocol=protocol,
                capabilities=capabilities,
                version=version,
                timestamp=time.time()
            )
            
            self.announced_services[service_name] = announcement
            
            # Start announcement task if not already running
            if not self.announcement_task:
                self.announcement_task = asyncio.create_task(self._announcement_broadcaster())
            
            logger.info(f"Announced service: {service_name} at {host}:{port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to announce service {service_name}: {e}")
            return False
    
    async def discover_services(self, timeout: float = 10.0) -> List[ServiceAnnouncement]:
        """Actively discover services"""
        try:
            if not self.discovery_enabled:
                logger.warning("Service discovery not enabled")
                return []
            
            # Send discovery request
            discovery_request = {
                "type": "discovery_request",
                "timestamp": time.time(),
                "requester": "service_discovery"
            }
            
            await self._broadcast_message(discovery_request)
            
            # Wait for responses
            await asyncio.sleep(timeout)
            
            # Return discovered services
            return list(self.discovered_services.values())
            
        except Exception as e:
            logger.error(f"Error during service discovery: {e}")
            return []
    
    async def get_service(self, service_name: str) -> Optional[ServiceAnnouncement]:
        """Get specific discovered service"""
        return self.discovered_services.get(service_name)
    
    async def get_services_by_capability(self, capability: str) -> List[ServiceAnnouncement]:
        """Get services that have specific capability"""
        return [
            service for service in self.discovered_services.values()
            if capability in service.capabilities
        ]
    
    def register_discovery_callback(self, callback: callable):
        """Register callback for service discovery events"""
        if callback not in self.discovery_callbacks:
            self.discovery_callbacks.append(callback)
            logger.info("Registered discovery callback")
    
    async def _setup_sockets(self) -> bool:
        """Setup UDP sockets for discovery"""
        try:
            # Broadcast socket
            self.broadcast_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.broadcast_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            self.broadcast_socket.setblocking(False)
            
            # Listen socket
            self.listen_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.listen_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.listen_socket.bind(('', self.discovery_port))
            self.listen_socket.setblocking(False)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup discovery sockets: {e}")
            return False
    
    async def _announcement_broadcaster(self):
        """Broadcast service announcements periodically"""
        while self.discovery_enabled:
            try:
                for service_name, announcement in self.announced_services.items():
                    # Update timestamp
                    announcement.timestamp = time.time()
                    
                    # Broadcast announcement
                    message = {
                        "type": "service_announcement",
                        "service": announcement.to_dict()
                    }
                    
                    await self._broadcast_message(message)
                
                await asyncio.sleep(self.announcement_interval)
                
            except Exception as e:
                logger.error(f"Error in announcement broadcaster: {e}")
                await asyncio.sleep(5)
    
    async def _discovery_listener(self):
        """Listen for discovery messages"""
        while self.discovery_enabled:
            try:
                # Use asyncio to make socket non-blocking
                loop = asyncio.get_event_loop()
                data, addr = await loop.sock_recvfrom(self.listen_socket, 4096)
                
                message = json.loads(data.decode('utf-8'))
                await self._handle_discovery_message(message, addr)
                
            except asyncio.CancelledError:
                break
            except json.JSONDecodeError:
                logger.warning("Received invalid JSON in discovery message")
            except Exception as e:
                logger.error(f"Error in discovery listener: {e}")
                await asyncio.sleep(1)
    
    async def _handle_discovery_message(self, message: Dict[str, Any], addr: tuple):
        """Handle received discovery message"""
        try:
            message_type = message.get("type")
            
            if message_type == "service_announcement":
                await self._handle_service_announcement(message.get("service"), addr)
            
            elif message_type == "discovery_request":
                await self._handle_discovery_request(addr)
            
            elif message_type == "service_response":
                await self._handle_service_response(message.get("service"), addr)
                
        except Exception as e:
            logger.error(f"Error handling discovery message: {e}")
    
    async def _handle_service_announcement(self, service_data: Dict[str, Any], addr: tuple):
        """Handle service announcement"""
        try:
            if not service_data:
                return
            
            announcement = ServiceAnnouncement.from_dict(service_data)
            service_name = announcement.service_name
            
            # Check if this is a new service or updated service
            is_new_service = service_name not in self.discovered_services
            
            # Update discovered services
            self.discovered_services[service_name] = announcement
            
            logger.info(f"{'Discovered new' if is_new_service else 'Updated'} service: {service_name} at {announcement.host}:{announcement.port}")
            
            # Notify callbacks
            for callback in self.discovery_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback("service_discovered" if is_new_service else "service_updated", announcement)
                    else:
                        callback("service_discovered" if is_new_service else "service_updated", announcement)
                except Exception as e:
                    logger.error(f"Error in discovery callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling service announcement: {e}")
    
    async def _handle_discovery_request(self, addr: tuple):
        """Handle discovery request by responding with our services"""
        try:
            for service_name, announcement in self.announced_services.items():
                response = {
                    "type": "service_response",
                    "service": announcement.to_dict()
                }
                
                await self._send_message(response, addr)
                
        except Exception as e:
            logger.error(f"Error handling discovery request: {e}")
    
    async def _handle_service_response(self, service_data: Dict[str, Any], addr: tuple):
        """Handle service response"""
        # Same as service announcement
        await self._handle_service_announcement(service_data, addr)
    
    async def _broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to discovery network"""
        try:
            if not self.broadcast_socket:
                return
            
            data = json.dumps(message).encode('utf-8')
            
            # Broadcast to local network
            loop = asyncio.get_event_loop()
            await loop.sock_sendto(
                self.broadcast_socket,
                data,
                ('<broadcast>', self.discovery_port)
            )
            
        except Exception as e:
            logger.error(f"Error broadcasting message: {e}")
    
    async def _send_message(self, message: Dict[str, Any], addr: tuple):
        """Send message to specific address"""
        try:
            if not self.broadcast_socket:
                return
            
            data = json.dumps(message).encode('utf-8')
            
            loop = asyncio.get_event_loop()
            await loop.sock_sendto(
                self.broadcast_socket,
                data,
                (addr[0], self.discovery_port)
            )
            
        except Exception as e:
            logger.error(f"Error sending message to {addr}: {e}")
    
    async def _cleanup_expired_services(self):
        """Clean up expired services"""
        while self.discovery_enabled:
            try:
                current_time = time.time()
                expired_services = []
                
                for service_name, announcement in self.discovered_services.items():
                    if current_time - announcement.timestamp > self.service_timeout:
                        expired_services.append(service_name)
                
                # Remove expired services
                for service_name in expired_services:
                    del self.discovered_services[service_name]
                    logger.info(f"Removed expired service: {service_name}")
                    
                    # Notify callbacks
                    for callback in self.discovery_callbacks:
                        try:
                            if asyncio.iscoroutinefunction(callback):
                                await callback("service_expired", service_name)
                            else:
                                callback("service_expired", service_name)
                        except Exception as e:
                            logger.error(f"Error in expiry callback: {e}")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(30)
    
    async def stop_discovery(self):
        """Stop service discovery"""
        try:
            logger.info("Stopping service discovery...")
            
            self.discovery_enabled = False
            
            # Cancel tasks
            if self.announcement_task:
                self.announcement_task.cancel()
                try:
                    await self.announcement_task
                except asyncio.CancelledError:
                    pass
            
            if self.listener_task:
                self.listener_task.cancel()
                try:
                    await self.listener_task
                except asyncio.CancelledError:
                    pass
            
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # Close sockets
            if self.broadcast_socket:
                self.broadcast_socket.close()
                self.broadcast_socket = None
            
            if self.listen_socket:
                self.listen_socket.close()
                self.listen_socket = None
            
            logger.info("Service discovery stopped")
            
        except Exception as e:
            logger.error(f"Error stopping service discovery: {e}")
    
    async def get_discovery_stats(self) -> Dict[str, Any]:
        """Get discovery statistics"""
        return {
            "discovery_enabled": self.discovery_enabled,
            "announced_services": len(self.announced_services),
            "discovered_services": len(self.discovered_services),
            "discovery_callbacks": len(self.discovery_callbacks),
            "announcement_interval": self.announcement_interval,
            "service_timeout": self.service_timeout,
            "services": {
                "announced": [s.service_name for s in self.announced_services.values()],
                "discovered": [s.service_name for s in self.discovered_services.values()]
            }
        }


class AutoServiceRegistration:
    """Automatically register services with communication coordinator"""
    
    def __init__(self, communication_manager, service_discovery: ServiceDiscovery):
        self.communication_manager = communication_manager
        self.service_discovery = service_discovery
        
    async def start_auto_registration(self):
        """Start automatic service registration"""
        try:
            # Register discovery callback
            self.service_discovery.register_discovery_callback(self._on_service_discovered)
            
            # Discover existing services
            discovered = await self.service_discovery.discover_services(timeout=5.0)
            
            for service in discovered:
                await self._register_discovered_service(service)
            
            logger.info("Auto service registration started")
            
        except Exception as e:
            logger.error(f"Failed to start auto registration: {e}")
    
    async def _on_service_discovered(self, event_type: str, service_data):
        """Handle service discovery events"""
        try:
            if event_type == "service_discovered":
                await self._register_discovered_service(service_data)
            elif event_type == "service_expired":
                await self._unregister_service(service_data)
                
        except Exception as e:
            logger.error(f"Error handling service discovery event: {e}")
    
    async def _register_discovered_service(self, announcement: ServiceAnnouncement):
        """Register discovered service with communication coordinator"""
        try:
            coordinator = self.communication_manager.coordinator
            
            success = await coordinator.register_service(
                service_name=announcement.service_name,
                host=announcement.host,
                port=announcement.port,
                protocol=announcement.protocol
            )
            
            if success:
                logger.info(f"Auto-registered discovered service: {announcement.service_name}")
            else:
                logger.warning(f"Failed to auto-register service: {announcement.service_name}")
                
        except Exception as e:
            logger.error(f"Error registering discovered service: {e}")
    
    async def _unregister_service(self, service_name: str):
        """Unregister expired service"""
        try:
            coordinator = self.communication_manager.coordinator
            
            success = await coordinator.service_registry.unregister_service(service_name)
            
            if success:
                logger.info(f"Auto-unregistered expired service: {service_name}")
                
        except Exception as e:
            logger.error(f"Error unregistering service: {e}")
