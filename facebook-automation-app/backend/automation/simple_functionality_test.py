
"""
Simple functionality test for API interception modules
"""
import asyncio
import sys
import os

# Add path
sys.path.insert(0, os.path.dirname(__file__))

async def simple_test():
    """Simple test function"""
    try:
        # Test basic imports
        from facebook_comment_interceptor import InterceptionConfig
        from stealth_browser_manager import create_facebook_profile, ProxyConfig
        from network_monitor import create_facebook_network_monitor
        from smart_scrolling import create_smart_scroller
        from facebook_data_parser import create_facebook_parser
        
        print("✅ All imports successful")
        
        # Test basic object creation
        config = InterceptionConfig()
        print(f"✅ InterceptionConfig created: max_comments={config.max_comments}")
        
        proxy_config = ProxyConfig(proxy_type='none')
        print(f"✅ ProxyConfig created: type={proxy_config.proxy_type}")
        
        profile = create_facebook_profile('test', 'Test Profile', './test', proxy_config)
        print(f"✅ Profile created: {profile.profile_name}")
        
        monitor = create_facebook_network_monitor()
        print("✅ Network monitor created")
        
        scroller = create_smart_scroller()
        print("✅ Smart scroller created")
        
        parser = create_facebook_parser()
        print("✅ Data parser created")
        
        print("🎉 All basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(simple_test())
