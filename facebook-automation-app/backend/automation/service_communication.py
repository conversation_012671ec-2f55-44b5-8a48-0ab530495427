"""
ServiceCommunication - Advanced inter-service communication layer
Handles HTTP, WebSocket, and message queue communication between services
"""
import asyncio
import json
import time
import aiohttp
import websockets
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, asdict
from loguru import logger
import uuid

from .interfaces import ServiceEvent, EventType


@dataclass
class ServiceEndpoint:
    """Service endpoint configuration"""
    service_name: str
    host: str
    port: int
    protocol: str = "http"  # http, https, ws, wss
    health_path: str = "/health"
    
    @property
    def base_url(self) -> str:
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def health_url(self) -> str:
        return f"{self.base_url}{self.health_path}"


@dataclass
class MessageEnvelope:
    """Message envelope for inter-service communication"""
    message_id: str
    source_service: str
    target_service: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: float
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl: Optional[float] = None  # Time to live in seconds


class ServiceRegistry:
    """Registry for managing service endpoints"""
    
    def __init__(self):
        self.services: Dict[str, ServiceEndpoint] = {}
        self.health_status: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
    
    async def register_service(self, endpoint: ServiceEndpoint) -> bool:
        """Register a service endpoint"""
        try:
            async with self._lock:
                self.services[endpoint.service_name] = endpoint
                self.health_status[endpoint.service_name] = {
                    "status": "unknown",
                    "last_check": 0.0,
                    "response_time": 0.0
                }
            
            logger.info(f"Registered service: {endpoint.service_name} at {endpoint.base_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register service {endpoint.service_name}: {e}")
            return False
    
    async def unregister_service(self, service_name: str) -> bool:
        """Unregister a service"""
        try:
            async with self._lock:
                if service_name in self.services:
                    del self.services[service_name]
                if service_name in self.health_status:
                    del self.health_status[service_name]
            
            logger.info(f"Unregistered service: {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister service {service_name}: {e}")
            return False
    
    async def get_service(self, service_name: str) -> Optional[ServiceEndpoint]:
        """Get service endpoint"""
        async with self._lock:
            return self.services.get(service_name)
    
    async def list_services(self) -> List[ServiceEndpoint]:
        """List all registered services"""
        async with self._lock:
            return list(self.services.values())
    
    async def check_service_health(self, service_name: str) -> Dict[str, Any]:
        """Check health of a specific service"""
        try:
            endpoint = await self.get_service(service_name)
            if not endpoint:
                return {"status": "not_registered"}
            
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    endpoint.health_url,
                    timeout=aiohttp.ClientTimeout(total=5.0)
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        health_data = await response.json()
                        status = {
                            "status": "healthy",
                            "last_check": time.time(),
                            "response_time": response_time,
                            "details": health_data
                        }
                    else:
                        status = {
                            "status": "unhealthy",
                            "last_check": time.time(),
                            "response_time": response_time,
                            "error": f"HTTP {response.status}"
                        }
            
            async with self._lock:
                self.health_status[service_name] = status
            
            return status
            
        except Exception as e:
            status = {
                "status": "error",
                "last_check": time.time(),
                "response_time": 0.0,
                "error": str(e)
            }
            
            async with self._lock:
                self.health_status[service_name] = status
            
            return status
    
    async def check_all_services_health(self) -> Dict[str, Dict[str, Any]]:
        """Check health of all registered services"""
        tasks = []
        service_names = list(self.services.keys())
        
        for service_name in service_names:
            task = asyncio.create_task(self.check_service_health(service_name))
            tasks.append((service_name, task))
        
        results = {}
        for service_name, task in tasks:
            try:
                results[service_name] = await task
            except Exception as e:
                results[service_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return results


class HTTPCommunicator:
    """HTTP-based communication between services"""
    
    def __init__(self, service_registry: ServiceRegistry):
        self.registry = service_registry
        self.session: Optional[aiohttp.ClientSession] = None
        self.request_timeout = 30.0
        self.retry_attempts = 3
        self.retry_delay = 1.0
    
    async def initialize(self) -> bool:
        """Initialize HTTP communicator"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.request_timeout)
            )
            logger.info("HTTPCommunicator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTPCommunicator: {e}")
            return False
    
    async def send_request(
        self,
        target_service: str,
        endpoint: str,
        method: str = "POST",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Send HTTP request to target service"""
        try:
            service_endpoint = await self.registry.get_service(target_service)
            if not service_endpoint:
                return {"success": False, "error": f"Service {target_service} not registered"}
            
            url = f"{service_endpoint.base_url}{endpoint}"
            
            # Prepare headers
            request_headers = {"Content-Type": "application/json"}
            if headers:
                request_headers.update(headers)
            
            # Retry logic
            last_error = None
            for attempt in range(self.retry_attempts):
                try:
                    if method.upper() == "GET":
                        async with self.session.get(url, headers=request_headers) as response:
                            result = await response.json()
                            return {"success": True, "data": result, "status": response.status}
                    
                    elif method.upper() == "POST":
                        async with self.session.post(
                            url, 
                            json=data, 
                            headers=request_headers
                        ) as response:
                            result = await response.json()
                            return {"success": True, "data": result, "status": response.status}
                    
                    else:
                        return {"success": False, "error": f"Unsupported method: {method}"}
                
                except Exception as e:
                    last_error = e
                    if attempt < self.retry_attempts - 1:
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                        continue
                    break
            
            return {"success": False, "error": f"Request failed after {self.retry_attempts} attempts: {last_error}"}
            
        except Exception as e:
            logger.error(f"Error sending request to {target_service}: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup(self):
        """Cleanup HTTP communicator"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            logger.info("HTTPCommunicator cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up HTTPCommunicator: {e}")


class WebSocketCommunicator:
    """WebSocket-based real-time communication"""
    
    def __init__(self, service_registry: ServiceRegistry):
        self.registry = service_registry
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self._lock = asyncio.Lock()
    
    async def connect_to_service(self, service_name: str, endpoint: str = "/ws") -> bool:
        """Connect to service via WebSocket"""
        try:
            service_endpoint = await self.registry.get_service(service_name)
            if not service_endpoint:
                logger.error(f"Service {service_name} not registered")
                return False
            
            # Convert HTTP to WebSocket URL
            ws_url = service_endpoint.base_url.replace("http://", "ws://").replace("https://", "wss://")
            ws_url += endpoint
            
            # Connect to WebSocket
            websocket = await websockets.connect(ws_url)
            
            async with self._lock:
                self.connections[service_name] = websocket
            
            # Start message listener
            asyncio.create_task(self._listen_to_service(service_name, websocket))
            
            logger.info(f"Connected to {service_name} via WebSocket")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to {service_name} via WebSocket: {e}")
            return False
    
    async def send_message(
        self,
        target_service: str,
        message: Dict[str, Any]
    ) -> bool:
        """Send message via WebSocket"""
        try:
            async with self._lock:
                if target_service not in self.connections:
                    logger.error(f"No WebSocket connection to {target_service}")
                    return False
                
                websocket = self.connections[target_service]
            
            # Send message
            await websocket.send(json.dumps(message))
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WebSocket message to {target_service}: {e}")
            return False
    
    async def _listen_to_service(self, service_name: str, websocket):
        """Listen for messages from service"""
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    # Handle message
                    if service_name in self.message_handlers:
                        await self.message_handlers[service_name](data)
                    else:
                        logger.debug(f"Received message from {service_name}: {data}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON from {service_name}: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket connection to {service_name} closed")
        except Exception as e:
            logger.error(f"Error listening to {service_name}: {e}")
        finally:
            # Clean up connection
            async with self._lock:
                if service_name in self.connections:
                    del self.connections[service_name]
    
    def register_message_handler(self, service_name: str, handler: Callable):
        """Register message handler for service"""
        self.message_handlers[service_name] = handler
        logger.info(f"Registered message handler for {service_name}")
    
    async def disconnect_from_service(self, service_name: str) -> bool:
        """Disconnect from service"""
        try:
            async with self._lock:
                if service_name in self.connections:
                    websocket = self.connections[service_name]
                    await websocket.close()
                    del self.connections[service_name]
            
            logger.info(f"Disconnected from {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from {service_name}: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup all WebSocket connections"""
        try:
            service_names = list(self.connections.keys())
            for service_name in service_names:
                await self.disconnect_from_service(service_name)
            
            logger.info("WebSocketCommunicator cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up WebSocketCommunicator: {e}")


class MessageQueue:
    """Simple in-memory message queue for async communication"""
    
    def __init__(self, max_size: int = 10000):
        self.queues: Dict[str, asyncio.Queue] = {}
        self.max_size = max_size
        self.subscribers: Dict[str, List[Callable]] = {}
        self._lock = asyncio.Lock()
        
        # Statistics
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0,
            "queue_overflows": 0,
            "active_queues": 0
        }
    
    async def create_queue(self, queue_name: str) -> bool:
        """Create a message queue"""
        try:
            async with self._lock:
                if queue_name not in self.queues:
                    self.queues[queue_name] = asyncio.Queue(maxsize=self.max_size)
                    self.subscribers[queue_name] = []
                    self.stats["active_queues"] = len(self.queues)
            
            logger.info(f"Created message queue: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create queue {queue_name}: {e}")
            return False
    
    async def send_message(
        self,
        queue_name: str,
        message: MessageEnvelope
    ) -> bool:
        """Send message to queue"""
        try:
            async with self._lock:
                if queue_name not in self.queues:
                    await self.create_queue(queue_name)
                
                queue = self.queues[queue_name]
            
            # Check TTL
            if message.ttl and time.time() - message.timestamp > message.ttl:
                logger.warning(f"Message {message.message_id} expired")
                return False
            
            # Try to put message in queue
            try:
                queue.put_nowait(message)
                self.stats["messages_sent"] += 1
                
                # Notify subscribers
                await self._notify_subscribers(queue_name, message)
                
                return True
                
            except asyncio.QueueFull:
                self.stats["queue_overflows"] += 1
                logger.warning(f"Queue {queue_name} is full, dropping message")
                return False
                
        except Exception as e:
            logger.error(f"Error sending message to queue {queue_name}: {e}")
            return False
    
    async def receive_message(
        self,
        queue_name: str,
        timeout: Optional[float] = None
    ) -> Optional[MessageEnvelope]:
        """Receive message from queue"""
        try:
            async with self._lock:
                if queue_name not in self.queues:
                    return None
                
                queue = self.queues[queue_name]
            
            # Get message with timeout
            if timeout:
                message = await asyncio.wait_for(queue.get(), timeout=timeout)
            else:
                message = await queue.get()
            
            self.stats["messages_received"] += 1
            queue.task_done()
            
            return message
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error receiving message from queue {queue_name}: {e}")
            return None
    
    async def subscribe(self, queue_name: str, callback: Callable) -> bool:
        """Subscribe to queue messages"""
        try:
            async with self._lock:
                if queue_name not in self.subscribers:
                    self.subscribers[queue_name] = []
                
                if callback not in self.subscribers[queue_name]:
                    self.subscribers[queue_name].append(callback)
            
            logger.info(f"Subscribed to queue: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error subscribing to queue {queue_name}: {e}")
            return False
    
    async def _notify_subscribers(self, queue_name: str, message: MessageEnvelope):
        """Notify subscribers of new message"""
        try:
            subscribers = self.subscribers.get(queue_name, [])
            
            for callback in subscribers:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(message)
                    else:
                        callback(message)
                except Exception as e:
                    logger.error(f"Error in subscriber callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error notifying subscribers: {e}")
    
    async def get_queue_stats(self, queue_name: str) -> Dict[str, Any]:
        """Get statistics for specific queue"""
        try:
            async with self._lock:
                if queue_name not in self.queues:
                    return {"error": "Queue not found"}
                
                queue = self.queues[queue_name]
                return {
                    "queue_size": queue.qsize(),
                    "max_size": self.max_size,
                    "subscribers": len(self.subscribers.get(queue_name, []))
                }
                
        except Exception as e:
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup message queue"""
        try:
            async with self._lock:
                # Clear all queues
                for queue_name, queue in self.queues.items():
                    while not queue.empty():
                        try:
                            queue.get_nowait()
                            queue.task_done()
                        except asyncio.QueueEmpty:
                            break
                
                self.queues.clear()
                self.subscribers.clear()
                self.stats["active_queues"] = 0
            
            logger.info("MessageQueue cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up MessageQueue: {e}")
