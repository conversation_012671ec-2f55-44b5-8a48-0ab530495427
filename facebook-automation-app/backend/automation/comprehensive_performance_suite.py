"""
ComprehensivePerformanceSuite - Complete performance testing and optimization suite
"""
import asyncio
import time
import json
import os
from typing import Dict, Any, List
from loguru import logger

from .performance_testing import PerformanceBenchmark, PerformanceReporter
from .load_testing import LoadTester, StressTestRunner
from .memory_profiler import MemoryProfiler
from .communication_test import CommunicationTester
from .performance_comparison import PerformanceComparator


class ComprehensivePerformanceSuite:
    """Complete performance testing and optimization suite"""
    
    def __init__(self, output_dir: str = "performance_results"):
        self.output_dir = output_dir
        self.results = {}
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize test components
        self.benchmark = PerformanceBenchmark()
        self.load_tester = LoadTester()
        self.stress_tester = StressTestRunner()
        self.memory_profiler = MemoryProfiler()
        self.communication_tester = CommunicationTester()
        self.performance_comparator = PerformanceComparator()
        
    async def run_complete_test_suite(self) -> Dict[str, Any]:
        """Run complete performance test suite"""
        logger.info("🚀 Starting Comprehensive Performance Test Suite")
        logger.info("="*80)
        
        suite_start_time = time.time()
        
        # Test execution plan
        test_plan = [
            ("Communication Tests", self._run_communication_tests),
            ("Memory Profiling", self._run_memory_profiling),
            ("Performance Benchmarks", self._run_performance_benchmarks),
            ("Load Testing", self._run_load_testing),
            ("Stress Testing", self._run_stress_testing),
            ("Performance Comparison", self._run_performance_comparison)
        ]
        
        # Execute test plan
        for test_name, test_func in test_plan:
            logger.info(f"\n📋 Running {test_name}...")
            try:
                result = await test_func()
                self.results[test_name.lower().replace(" ", "_")] = result
                logger.info(f"✅ {test_name} completed successfully")
            except Exception as e:
                logger.error(f"❌ {test_name} failed: {e}")
                self.results[test_name.lower().replace(" ", "_")] = {"error": str(e)}
        
        suite_duration = time.time() - suite_start_time
        
        # Generate comprehensive report
        comprehensive_report = await self._generate_comprehensive_report(suite_duration)
        
        # Save all results
        await self._save_results()
        
        logger.info(f"\n🎉 Performance Test Suite completed in {suite_duration:.1f} seconds")
        logger.info(f"📁 Results saved to {self.output_dir}/")
        
        return comprehensive_report
    
    async def _run_communication_tests(self) -> Dict[str, Any]:
        """Run communication system tests"""
        return await self.communication_tester.run_all_tests()
    
    async def _run_memory_profiling(self) -> Dict[str, Any]:
        """Run memory profiling tests"""
        # Start memory profiling
        await self.memory_profiler.start_profiling()
        
        # Simulate some workload for memory analysis
        logger.info("Running memory profiling workload...")
        
        # Import and run a light workload
        from .hybrid_integration import HybridServiceFactory
        
        service = HybridServiceFactory.create_development_service()
        if await service.initialize():
            # Run a few scraping operations to generate memory usage
            for i in range(3):
                await service.scrape_facebook_post_uids(
                    profile_id=f"memory_test_{i}",
                    post_url=f"https://facebook.com/test/memory/{i}"
                )
                await asyncio.sleep(2)
            
            await service.cleanup()
        
        # Stop profiling and get results
        return await self.memory_profiler.stop_profiling()
    
    async def _run_performance_benchmarks(self) -> Dict[str, Any]:
        """Run performance benchmarks"""
        return await self.benchmark.run_comprehensive_benchmark()
    
    async def _run_load_testing(self) -> Dict[str, Any]:
        """Run load testing"""
        return await self.load_tester.run_all_load_tests()
    
    async def _run_stress_testing(self) -> Dict[str, Any]:
        """Run stress testing"""
        return await self.stress_tester.find_system_limits()
    
    async def _run_performance_comparison(self) -> Dict[str, Any]:
        """Run performance comparison"""
        return await self.performance_comparator.run_comprehensive_comparison(test_iterations=2)
    
    async def _generate_comprehensive_report(self, suite_duration: float) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            report = {
                "timestamp": time.time(),
                "suite_duration": suite_duration,
                "executive_summary": self._generate_executive_summary(),
                "detailed_results": self.results,
                "performance_metrics": self._extract_key_metrics(),
                "recommendations": self._generate_comprehensive_recommendations(),
                "system_health": self._assess_system_health()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return {"error": str(e)}
    
    def _generate_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary of all tests"""
        summary = {
            "overall_status": "unknown",
            "critical_issues": [],
            "performance_highlights": [],
            "system_readiness": "unknown"
        }
        
        try:
            critical_issues = []
            performance_highlights = []
            
            # Analyze communication tests
            comm_results = self.results.get("communication_tests", {})
            if "summary" in comm_results:
                comm_summary = comm_results["summary"]
                if comm_summary.get("overall_status") == "FAILED":
                    critical_issues.append("Communication system has failures")
                else:
                    performance_highlights.append("Communication system is healthy")
            
            # Analyze memory profiling
            memory_results = self.results.get("memory_profiling", {})
            if "memory_statistics" in memory_results:
                memory_stats = memory_results["memory_statistics"]
                current_mb = memory_stats.get("current_mb", 0)
                if current_mb > 1500:
                    critical_issues.append(f"High memory usage: {current_mb:.1f}MB")
                elif current_mb < 500:
                    performance_highlights.append("Efficient memory usage")
            
            # Analyze load testing
            load_results = self.results.get("load_testing", {})
            if "test_results" in load_results:
                for config_name, result in load_results["test_results"].items():
                    success_rate = result.get("performance", {}).get("success_rate", 0)
                    if success_rate < 0.8:
                        critical_issues.append(f"Low success rate in {config_name}: {success_rate:.1%}")
                    elif success_rate > 0.95:
                        performance_highlights.append(f"Excellent success rate in {config_name}")
            
            # Determine overall status
            if critical_issues:
                overall_status = "critical" if len(critical_issues) > 2 else "warning"
            else:
                overall_status = "healthy"
            
            # Determine system readiness
            if overall_status == "healthy" and len(performance_highlights) > 2:
                system_readiness = "production_ready"
            elif overall_status == "warning":
                system_readiness = "needs_optimization"
            else:
                system_readiness = "not_ready"
            
            summary.update({
                "overall_status": overall_status,
                "critical_issues": critical_issues,
                "performance_highlights": performance_highlights,
                "system_readiness": system_readiness
            })
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            summary["error"] = str(e)
        
        return summary
    
    def _extract_key_metrics(self) -> Dict[str, Any]:
        """Extract key performance metrics from all tests"""
        metrics = {}
        
        try:
            # Communication metrics
            comm_results = self.results.get("communication_tests", {})
            if "summary" in comm_results:
                metrics["communication_success_rate"] = comm_results["summary"].get("success_rate", 0)
            
            # Memory metrics
            memory_results = self.results.get("memory_profiling", {})
            if "memory_statistics" in memory_results:
                memory_stats = memory_results["memory_statistics"]
                metrics["peak_memory_mb"] = memory_stats.get("peak_mb", 0)
                metrics["memory_growth_mb"] = memory_stats.get("growth_mb", 0)
            
            # Load testing metrics
            load_results = self.results.get("load_testing", {})
            if "test_results" in load_results:
                max_throughput = 0
                avg_response_time = 0
                test_count = 0
                
                for result in load_results["test_results"].values():
                    perf = result.get("performance", {})
                    throughput = perf.get("throughput_requests_per_second", 0)
                    response_time = perf.get("avg_response_time", 0)
                    
                    if throughput > max_throughput:
                        max_throughput = throughput
                    
                    if response_time > 0:
                        avg_response_time += response_time
                        test_count += 1
                
                metrics["max_throughput_rps"] = max_throughput
                metrics["avg_response_time_s"] = avg_response_time / test_count if test_count > 0 else 0
            
            # Stress testing metrics
            stress_results = self.results.get("stress_testing", {})
            if "system_limits" in stress_results:
                limits = stress_results["system_limits"]
                optimal = limits.get("optimal_operating_point", {})
                maximum = limits.get("maximum_capacity", {})
                
                metrics["optimal_concurrent_users"] = optimal.get("concurrent_users", 0)
                metrics["max_concurrent_users"] = maximum.get("concurrent_users", 0)
            
        except Exception as e:
            logger.error(f"Error extracting key metrics: {e}")
            metrics["extraction_error"] = str(e)
        
        return metrics
    
    def _generate_comprehensive_recommendations(self) -> List[str]:
        """Generate comprehensive optimization recommendations"""
        recommendations = []
        
        try:
            # Memory recommendations
            memory_results = self.results.get("memory_profiling", {})
            if "recommendations" in memory_results:
                memory_recs = memory_results["recommendations"][:3]  # Top 3
                recommendations.extend([f"Memory: {rec}" for rec in memory_recs])
            
            # Load testing recommendations
            load_results = self.results.get("load_testing", {})
            if "recommendations" in load_results:
                load_recs = load_results["recommendations"][:3]  # Top 3
                recommendations.extend([f"Load: {rec}" for rec in load_recs])
            
            # Performance comparison recommendations
            comparison_results = self.results.get("performance_comparison", {})
            if "recommendations" in comparison_results:
                comp_recs = list(comparison_results["recommendations"].values())[:2]  # Top 2
                recommendations.extend([f"Performance: {rec}" for rec in comp_recs])
            
            # General system recommendations
            executive_summary = self._generate_executive_summary()
            if executive_summary.get("overall_status") == "critical":
                recommendations.insert(0, "CRITICAL: Address all critical issues before production deployment")
            elif executive_summary.get("overall_status") == "warning":
                recommendations.insert(0, "WARNING: Optimize system performance before high-load scenarios")
            
            # Add strategic recommendations
            recommendations.extend([
                "Implement continuous performance monitoring",
                "Set up automated performance regression testing",
                "Create performance dashboards for real-time monitoring",
                "Establish performance SLAs and alerting thresholds"
            ])
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations.append(f"Error generating recommendations: {e}")
        
        return recommendations[:10]  # Limit to top 10
    
    def _assess_system_health(self) -> Dict[str, Any]:
        """Assess overall system health"""
        health = {
            "overall_score": 0,
            "component_scores": {},
            "health_status": "unknown",
            "readiness_assessment": "unknown"
        }
        
        try:
            scores = {}
            
            # Communication health (0-100)
            comm_results = self.results.get("communication_tests", {})
            if "summary" in comm_results:
                comm_success_rate = comm_results["summary"].get("success_rate", 0)
                scores["communication"] = comm_success_rate * 100
            
            # Memory health (0-100)
            memory_results = self.results.get("memory_profiling", {})
            if "memory_statistics" in memory_results:
                current_mb = memory_results["memory_statistics"].get("current_mb", 0)
                # Score based on memory usage (lower is better)
                if current_mb < 500:
                    scores["memory"] = 100
                elif current_mb < 1000:
                    scores["memory"] = 80
                elif current_mb < 1500:
                    scores["memory"] = 60
                else:
                    scores["memory"] = 30
            
            # Load testing health (0-100)
            load_results = self.results.get("load_testing", {})
            if "test_results" in load_results:
                success_rates = []
                for result in load_results["test_results"].values():
                    success_rate = result.get("performance", {}).get("success_rate", 0)
                    success_rates.append(success_rate)
                
                if success_rates:
                    avg_success_rate = sum(success_rates) / len(success_rates)
                    scores["load_handling"] = avg_success_rate * 100
            
            # Calculate overall score
            if scores:
                overall_score = sum(scores.values()) / len(scores)
                health["overall_score"] = overall_score
                health["component_scores"] = scores
                
                # Determine health status
                if overall_score >= 90:
                    health["health_status"] = "excellent"
                    health["readiness_assessment"] = "production_ready"
                elif overall_score >= 80:
                    health["health_status"] = "good"
                    health["readiness_assessment"] = "production_ready_with_monitoring"
                elif overall_score >= 70:
                    health["health_status"] = "fair"
                    health["readiness_assessment"] = "needs_optimization"
                else:
                    health["health_status"] = "poor"
                    health["readiness_assessment"] = "not_production_ready"
            
        except Exception as e:
            logger.error(f"Error assessing system health: {e}")
            health["assessment_error"] = str(e)
        
        return health
    
    async def _save_results(self):
        """Save all test results to files"""
        try:
            # Save individual test results
            for test_name, result in self.results.items():
                filename = f"{self.output_dir}/{test_name}_results.json"
                with open(filename, "w") as f:
                    json.dump(result, f, indent=2, default=str)
            
            # Save comprehensive report
            comprehensive_report = await self._generate_comprehensive_report(0)
            with open(f"{self.output_dir}/comprehensive_report.json", "w") as f:
                json.dump(comprehensive_report, f, indent=2, default=str)
            
            # Generate and save text report
            text_report = self._generate_text_report(comprehensive_report)
            with open(f"{self.output_dir}/performance_report.txt", "w") as f:
                f.write(text_report)
            
            logger.info(f"All results saved to {self.output_dir}/")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _generate_text_report(self, comprehensive_report: Dict[str, Any]) -> str:
        """Generate human-readable text report"""
        report = []
        report.append("="*100)
        report.append("COMPREHENSIVE PERFORMANCE TEST SUITE REPORT")
        report.append("="*100)
        
        # Executive Summary
        exec_summary = comprehensive_report.get("executive_summary", {})
        report.append(f"\n🎯 EXECUTIVE SUMMARY")
        report.append("-" * 50)
        report.append(f"Overall Status: {exec_summary.get('overall_status', 'unknown').upper()}")
        report.append(f"System Readiness: {exec_summary.get('system_readiness', 'unknown').upper()}")
        
        # Critical Issues
        critical_issues = exec_summary.get("critical_issues", [])
        if critical_issues:
            report.append(f"\n🚨 CRITICAL ISSUES")
            report.append("-" * 50)
            for issue in critical_issues:
                report.append(f"  ❌ {issue}")
        
        # Performance Highlights
        highlights = exec_summary.get("performance_highlights", [])
        if highlights:
            report.append(f"\n✨ PERFORMANCE HIGHLIGHTS")
            report.append("-" * 50)
            for highlight in highlights:
                report.append(f"  ✅ {highlight}")
        
        # Key Metrics
        metrics = comprehensive_report.get("performance_metrics", {})
        if metrics:
            report.append(f"\n📊 KEY PERFORMANCE METRICS")
            report.append("-" * 50)
            for metric, value in metrics.items():
                if isinstance(value, float):
                    report.append(f"  {metric}: {value:.2f}")
                else:
                    report.append(f"  {metric}: {value}")
        
        # System Health
        health = comprehensive_report.get("system_health", {})
        if health:
            report.append(f"\n🏥 SYSTEM HEALTH ASSESSMENT")
            report.append("-" * 50)
            report.append(f"Overall Score: {health.get('overall_score', 0):.1f}/100")
            report.append(f"Health Status: {health.get('health_status', 'unknown').upper()}")
            report.append(f"Readiness: {health.get('readiness_assessment', 'unknown').upper()}")
        
        # Recommendations
        recommendations = comprehensive_report.get("recommendations", [])
        if recommendations:
            report.append(f"\n💡 OPTIMIZATION RECOMMENDATIONS")
            report.append("-" * 50)
            for i, rec in enumerate(recommendations, 1):
                report.append(f"  {i:2d}. {rec}")
        
        report.append(f"\n📁 Detailed results available in JSON files")
        report.append("="*100)
        
        return "\n".join(report)


async def main():
    """Main comprehensive performance testing function"""
    try:
        logger.info("🚀 Starting Comprehensive Performance Test Suite")
        
        # Create test suite
        suite = ComprehensivePerformanceSuite()
        
        # Run complete test suite
        results = await suite.run_complete_test_suite()
        
        # Print summary
        print("\n" + "="*100)
        print("PERFORMANCE TEST SUITE COMPLETED")
        print("="*100)
        
        exec_summary = results.get("executive_summary", {})
        print(f"\nOverall Status: {exec_summary.get('overall_status', 'unknown').upper()}")
        print(f"System Readiness: {exec_summary.get('system_readiness', 'unknown').upper()}")
        
        health = results.get("system_health", {})
        print(f"Health Score: {health.get('overall_score', 0):.1f}/100")
        
        print(f"\n📁 Detailed results saved to: {suite.output_dir}/")
        print("="*100)
        
    except Exception as e:
        logger.error(f"Comprehensive performance testing failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run comprehensive performance testing
    asyncio.run(main())
