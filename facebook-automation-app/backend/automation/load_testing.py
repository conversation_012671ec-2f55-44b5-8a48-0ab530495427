"""
LoadTesting - High-load performance testing for hybrid system
"""
import asyncio
import time
import statistics
import random
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from loguru import logger
import json

from .hybrid_integration import HybridServiceFactory
from .performance_testing import PerformanceMetrics, PerformanceProfiler


@dataclass
class LoadTestConfig:
    """Load test configuration"""
    name: str
    concurrent_users: int
    requests_per_user: int
    ramp_up_time: float  # seconds
    test_duration: float  # seconds
    think_time_min: float  # seconds between requests
    think_time_max: float  # seconds between requests


class LoadTestUser:
    """Simulated user for load testing"""
    
    def __init__(self, user_id: int, config: LoadTestConfig):
        self.user_id = user_id
        self.config = config
        self.service = None
        self.requests_completed = 0
        self.requests_failed = 0
        self.total_response_time = 0.0
        self.errors: List[str] = []
        
    async def initialize(self) -> bool:
        """Initialize user's service"""
        try:
            self.service = HybridServiceFactory.create_service(
                performance_mode="balanced",
                enable_fallback=True
            )
            
            return await self.service.initialize()
            
        except Exception as e:
            logger.error(f"User {self.user_id} initialization failed: {e}")
            return False
    
    async def run_load_test(self) -> Dict[str, Any]:
        """Run load test for this user"""
        start_time = time.time()
        end_time = start_time + self.config.test_duration
        
        while time.time() < end_time and self.requests_completed < self.config.requests_per_user:
            try:
                # Perform request
                request_start = time.time()
                
                result = await self.service.scrape_facebook_post_uids(
                    profile_id=f"load_test_user_{self.user_id}",
                    post_url=f"https://facebook.com/test/load/{self.user_id}/{self.requests_completed}"
                )
                
                request_time = time.time() - request_start
                self.total_response_time += request_time
                
                if result.get("success", False):
                    self.requests_completed += 1
                else:
                    self.requests_failed += 1
                    self.errors.append(result.get("error", "Unknown error"))
                
                # Think time (simulate user behavior)
                think_time = random.uniform(self.config.think_time_min, self.config.think_time_max)
                await asyncio.sleep(think_time)
                
            except Exception as e:
                self.requests_failed += 1
                self.errors.append(str(e))
                logger.error(f"User {self.user_id} request failed: {e}")
        
        # Calculate metrics
        total_requests = self.requests_completed + self.requests_failed
        avg_response_time = self.total_response_time / self.requests_completed if self.requests_completed > 0 else 0
        
        return {
            "user_id": self.user_id,
            "requests_completed": self.requests_completed,
            "requests_failed": self.requests_failed,
            "total_requests": total_requests,
            "success_rate": self.requests_completed / total_requests if total_requests > 0 else 0,
            "avg_response_time": avg_response_time,
            "total_response_time": self.total_response_time,
            "errors": self.errors[:10]  # Keep only first 10 errors
        }
    
    async def cleanup(self):
        """Cleanup user resources"""
        try:
            if self.service:
                await self.service.cleanup()
        except Exception as e:
            logger.error(f"User {self.user_id} cleanup failed: {e}")


class LoadTester:
    """High-load performance tester"""
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
        self.test_configs = {
            "light_load": LoadTestConfig(
                name="Light Load",
                concurrent_users=5,
                requests_per_user=10,
                ramp_up_time=10.0,
                test_duration=60.0,
                think_time_min=1.0,
                think_time_max=3.0
            ),
            "medium_load": LoadTestConfig(
                name="Medium Load",
                concurrent_users=15,
                requests_per_user=20,
                ramp_up_time=30.0,
                test_duration=120.0,
                think_time_min=0.5,
                think_time_max=2.0
            ),
            "heavy_load": LoadTestConfig(
                name="Heavy Load",
                concurrent_users=30,
                requests_per_user=15,
                ramp_up_time=60.0,
                test_duration=180.0,
                think_time_min=0.1,
                think_time_max=1.0
            ),
            "stress_test": LoadTestConfig(
                name="Stress Test",
                concurrent_users=50,
                requests_per_user=10,
                ramp_up_time=30.0,
                test_duration=300.0,
                think_time_min=0.0,
                think_time_max=0.5
            )
        }
    
    async def run_all_load_tests(self) -> Dict[str, Any]:
        """Run all load test configurations"""
        logger.info("Starting comprehensive load testing...")
        
        results = {
            "timestamp": time.time(),
            "test_results": {},
            "system_performance": {},
            "recommendations": {}
        }
        
        for config_name, config in self.test_configs.items():
            logger.info(f"Running {config.name} test...")
            
            test_result = await self.run_load_test(config)
            results["test_results"][config_name] = test_result
            
            # Wait between tests to allow system recovery
            await asyncio.sleep(30)
        
        # Generate recommendations
        results["recommendations"] = self._generate_load_test_recommendations(results["test_results"])
        
        return results
    
    async def run_load_test(self, config: LoadTestConfig) -> Dict[str, Any]:
        """Run single load test configuration"""
        logger.info(f"Starting {config.name}: {config.concurrent_users} users, {config.requests_per_user} requests each")
        
        # Start system monitoring
        await self.profiler.start_monitoring()
        
        test_start_time = time.time()
        
        # Create users
        users = [LoadTestUser(i, config) for i in range(config.concurrent_users)]
        
        # Initialize users with ramp-up
        initialized_users = []
        ramp_up_delay = config.ramp_up_time / config.concurrent_users
        
        for user in users:
            if await user.initialize():
                initialized_users.append(user)
            else:
                logger.warning(f"Failed to initialize user {user.user_id}")
            
            # Ramp-up delay
            await asyncio.sleep(ramp_up_delay)
        
        logger.info(f"Initialized {len(initialized_users)}/{config.concurrent_users} users")
        
        # Run load test
        user_tasks = [asyncio.create_task(user.run_load_test()) for user in initialized_users]
        user_results = await asyncio.gather(*user_tasks, return_exceptions=True)
        
        # Cleanup users
        cleanup_tasks = [asyncio.create_task(user.cleanup()) for user in initialized_users]
        await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        test_end_time = time.time()
        
        # Stop monitoring and get system stats
        system_stats = await self.profiler.stop_monitoring()
        
        # Process results
        successful_results = [r for r in user_results if isinstance(r, dict)]
        failed_users = len(user_results) - len(successful_results)
        
        # Calculate aggregate metrics
        total_requests = sum(r["total_requests"] for r in successful_results)
        total_completed = sum(r["requests_completed"] for r in successful_results)
        total_failed = sum(r["requests_failed"] for r in successful_results)
        total_response_time = sum(r["total_response_time"] for r in successful_results)
        
        response_times = []
        for r in successful_results:
            if r["requests_completed"] > 0:
                response_times.append(r["avg_response_time"])
        
        test_duration = test_end_time - test_start_time
        throughput = total_completed / test_duration if test_duration > 0 else 0
        
        return {
            "config": {
                "name": config.name,
                "concurrent_users": config.concurrent_users,
                "requests_per_user": config.requests_per_user,
                "test_duration": config.test_duration
            },
            "execution": {
                "actual_duration": test_duration,
                "users_initialized": len(initialized_users),
                "users_failed": failed_users
            },
            "performance": {
                "total_requests": total_requests,
                "requests_completed": total_completed,
                "requests_failed": total_failed,
                "success_rate": total_completed / total_requests if total_requests > 0 else 0,
                "throughput_requests_per_second": throughput,
                "avg_response_time": statistics.mean(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "response_time_std": statistics.stdev(response_times) if len(response_times) > 1 else 0
            },
            "system_resources": system_stats,
            "user_results": successful_results[:5],  # Keep only first 5 for brevity
            "errors": self._collect_errors(successful_results)
        }
    
    def _collect_errors(self, user_results: List[Dict[str, Any]]) -> Dict[str, int]:
        """Collect and count errors from user results"""
        error_counts = {}
        
        for result in user_results:
            for error in result.get("errors", []):
                error_counts[error] = error_counts.get(error, 0) + 1
        
        # Return top 10 errors
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_errors[:10])
    
    def _generate_load_test_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on load test results"""
        recommendations = []
        
        # Analyze success rates
        for config_name, result in test_results.items():
            success_rate = result["performance"]["success_rate"]
            
            if success_rate < 0.95:
                recommendations.append(f"{config_name}: Low success rate ({success_rate:.1%}) - investigate error handling")
            
            if success_rate < 0.8:
                recommendations.append(f"{config_name}: Critical success rate - system may be overloaded")
        
        # Analyze response times
        response_times = []
        for result in test_results.values():
            if result["performance"]["avg_response_time"] > 0:
                response_times.append(result["performance"]["avg_response_time"])
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            if avg_response_time > 30:
                recommendations.append("High response times detected - consider performance optimization")
            elif avg_response_time > 60:
                recommendations.append("Critical response times - system requires immediate optimization")
        
        # Analyze throughput
        throughputs = []
        for result in test_results.values():
            throughputs.append(result["performance"]["throughput_requests_per_second"])
        
        if throughputs:
            max_throughput = max(throughputs)
            if max_throughput < 1:
                recommendations.append("Low throughput detected - consider scaling or optimization")
        
        # System resource recommendations
        for config_name, result in test_results.items():
            system_stats = result.get("system_resources", {})
            cpu_stats = system_stats.get("cpu_stats", {})
            memory_stats = system_stats.get("memory_stats", {})
            
            if cpu_stats.get("max", 0) > 90:
                recommendations.append(f"{config_name}: High CPU usage - consider CPU optimization")
            
            if memory_stats.get("peak_usage_mb", 0) > 2000:
                recommendations.append(f"{config_name}: High memory usage - consider memory optimization")
        
        if not recommendations:
            recommendations.append("System performance is within acceptable limits")
        
        return recommendations


class StressTestRunner:
    """Specialized stress testing for finding system limits"""
    
    def __init__(self):
        self.load_tester = LoadTester()
        
    async def find_system_limits(self) -> Dict[str, Any]:
        """Find system performance limits through progressive load testing"""
        logger.info("Starting system limit discovery...")
        
        results = {
            "timestamp": time.time(),
            "limit_tests": [],
            "system_limits": {},
            "breaking_point": None
        }
        
        # Progressive load testing
        user_counts = [10, 20, 30, 50, 75, 100, 150, 200]
        
        for user_count in user_counts:
            logger.info(f"Testing with {user_count} concurrent users...")
            
            # Create stress test config
            stress_config = LoadTestConfig(
                name=f"Stress Test {user_count} Users",
                concurrent_users=user_count,
                requests_per_user=5,
                ramp_up_time=30.0,
                test_duration=120.0,
                think_time_min=0.1,
                think_time_max=0.5
            )
            
            test_result = await self.load_tester.run_load_test(stress_config)
            results["limit_tests"].append(test_result)
            
            # Check if system is breaking
            success_rate = test_result["performance"]["success_rate"]
            avg_response_time = test_result["performance"]["avg_response_time"]
            
            if success_rate < 0.8 or avg_response_time > 60:
                results["breaking_point"] = {
                    "concurrent_users": user_count,
                    "success_rate": success_rate,
                    "avg_response_time": avg_response_time,
                    "reason": "Performance degradation detected"
                }
                logger.warning(f"Breaking point detected at {user_count} users")
                break
            
            # Wait between tests
            await asyncio.sleep(60)
        
        # Determine system limits
        results["system_limits"] = self._determine_system_limits(results["limit_tests"])
        
        return results
    
    def _determine_system_limits(self, limit_tests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Determine system limits from test results"""
        if not limit_tests:
            return {"error": "No test results available"}
        
        # Find optimal operating point
        optimal_point = None
        max_throughput = 0
        
        for test in limit_tests:
            perf = test["performance"]
            
            # Consider a test optimal if success rate > 95% and response time < 30s
            if perf["success_rate"] > 0.95 and perf["avg_response_time"] < 30:
                if perf["throughput_requests_per_second"] > max_throughput:
                    max_throughput = perf["throughput_requests_per_second"]
                    optimal_point = test
        
        # Find maximum capacity
        max_capacity = None
        for test in limit_tests:
            if test["performance"]["success_rate"] > 0.8:
                max_capacity = test
        
        return {
            "optimal_operating_point": {
                "concurrent_users": optimal_point["config"]["concurrent_users"] if optimal_point else 0,
                "throughput": max_throughput,
                "success_rate": optimal_point["performance"]["success_rate"] if optimal_point else 0,
                "avg_response_time": optimal_point["performance"]["avg_response_time"] if optimal_point else 0
            },
            "maximum_capacity": {
                "concurrent_users": max_capacity["config"]["concurrent_users"] if max_capacity else 0,
                "success_rate": max_capacity["performance"]["success_rate"] if max_capacity else 0,
                "avg_response_time": max_capacity["performance"]["avg_response_time"] if max_capacity else 0
            },
            "recommendations": [
                f"Optimal operating point: {optimal_point['config']['concurrent_users'] if optimal_point else 0} concurrent users",
                f"Maximum capacity: {max_capacity['config']['concurrent_users'] if max_capacity else 0} concurrent users",
                "Monitor system resources during peak load",
                "Implement auto-scaling based on load metrics"
            ]
        }


async def main():
    """Main load testing function"""
    try:
        logger.info("Starting Load Testing Suite")
        
        # Run comprehensive load tests
        load_tester = LoadTester()
        load_results = await load_tester.run_all_load_tests()
        
        # Save load test results
        with open("load_test_results.json", "w") as f:
            json.dump(load_results, f, indent=2)
        
        # Print summary
        print("\n" + "="*80)
        print("LOAD TESTING SUMMARY")
        print("="*80)
        
        for config_name, result in load_results["test_results"].items():
            perf = result["performance"]
            print(f"\n{result['config']['name']}:")
            print(f"  Users: {result['config']['concurrent_users']}")
            print(f"  Success Rate: {perf['success_rate']:.1%}")
            print(f"  Throughput: {perf['throughput_requests_per_second']:.2f} req/s")
            print(f"  Avg Response Time: {perf['avg_response_time']:.2f}s")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in load_results["recommendations"]:
            print(f"  • {rec}")
        
        print("\n" + "="*80)
        
        logger.info("Load testing completed. Results saved to load_test_results.json")
        
    except Exception as e:
        logger.error(f"Load testing failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run load testing
    asyncio.run(main())
