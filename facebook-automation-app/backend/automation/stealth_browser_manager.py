"""
Stealth Browser Manager with Off-screen positioning
Integrates with antidetect profile system for optimal Facebook automation
"""
import asyncio
import json
import os
import sys
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path

# Add zendriver_local to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'zendriver_local'))

import zendriver as zd
from loguru import logger


@dataclass
class ProxyConfig:
    """Proxy configuration for antidetect browser"""
    proxy_type: str  # 'http', 'https', 'socks5', 'ssh', 'none'
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None


@dataclass
class AntidetectProfile:
    """Antidetect browser profile configuration"""
    profile_id: str
    profile_name: str
    user_data_dir: str
    proxy_config: ProxyConfig
    user_agent: Optional[str] = None
    viewport_size: tuple = (1920, 1080)
    timezone: Optional[str] = None
    locale: Optional[str] = None
    webgl_vendor: Optional[str] = None
    webgl_renderer: Optional[str] = None
    canvas_fingerprint: Optional[str] = None


@dataclass
class StealthBrowserConfig:
    """Configuration for stealth browser with off-screen positioning"""
    headless: bool = False
    off_screen_position: tuple = (-2000, -2000)
    window_size: tuple = (1920, 1080)
    disable_images: bool = False
    disable_javascript: bool = False
    disable_plugins: bool = True
    disable_notifications: bool = True
    disable_popup_blocking: bool = True
    disable_web_security: bool = False
    user_agent_override: Optional[str] = None
    extra_browser_args: List[str] = None


class StealthBrowserManager:
    """
    Manages stealth browsers with off-screen positioning
    Integrates with antidetect profile system
    """
    
    def __init__(self):
        self.active_browsers: Dict[str, zd.Browser] = {}
        self.profile_configs: Dict[str, AntidetectProfile] = {}
        
    async def create_stealth_browser(
        self, 
        profile: AntidetectProfile,
        stealth_config: StealthBrowserConfig = None
    ) -> zd.Browser:
        """
        Create a stealth browser with off-screen positioning
        
        Args:
            profile: Antidetect profile configuration
            stealth_config: Stealth browser configuration
            
        Returns:
            Configured zendriver Browser instance
        """
        if stealth_config is None:
            stealth_config = StealthBrowserConfig()
        
        try:
            # Build browser arguments
            browser_args = self._build_browser_args(profile, stealth_config)
            
            # Create zendriver config
            zendriver_config = zd.Config(
                headless=stealth_config.headless,
                user_data_dir=profile.user_data_dir,
                browser_args=browser_args,
                sandbox=False  # Disable sandbox for better compatibility
            )
            
            # Start browser
            browser = await zd.start(config=zendriver_config)
            
            # Store browser reference
            self.active_browsers[profile.profile_id] = browser
            self.profile_configs[profile.profile_id] = profile
            
            # Apply additional stealth configurations
            await self._apply_stealth_configurations(browser, profile, stealth_config)
            
            logger.info(f"Created stealth browser for profile: {profile.profile_name}")
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create stealth browser: {e}")
            raise
    
    def _build_browser_args(
        self, 
        profile: AntidetectProfile, 
        stealth_config: StealthBrowserConfig
    ) -> List[str]:
        """Build browser arguments for stealth and off-screen positioning"""
        
        args = [
            # Off-screen positioning
            f"--window-position={stealth_config.off_screen_position[0]},{stealth_config.off_screen_position[1]}",
            f"--window-size={stealth_config.window_size[0]},{stealth_config.window_size[1]}",
            
            # Stealth arguments
            "--disable-blink-features=AutomationControlled",
            "--disable-dev-shm-usage",
            "--no-first-run",
            "--disable-default-apps",
            "--disable-extensions-except",
            "--disable-plugins-discovery",
            "--disable-translate",
            "--disable-ipc-flooding-protection",
            
            # Performance optimizations
            "--max_old_space_size=4096",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            
            # Privacy and security
            "--disable-background-networking",
            "--disable-sync",
            "--disable-features=TranslateUI",
            "--disable-component-extensions-with-background-pages",
        ]
        
        # Proxy configuration
        if profile.proxy_config.proxy_type != 'none' and profile.proxy_config.host:
            proxy_url = self._build_proxy_url(profile.proxy_config)
            args.append(f"--proxy-server={proxy_url}")
        
        # User agent override
        if stealth_config.user_agent_override:
            args.append(f"--user-agent={stealth_config.user_agent_override}")
        elif profile.user_agent:
            args.append(f"--user-agent={profile.user_agent}")
        
        # Disable images for performance
        if stealth_config.disable_images:
            args.append("--blink-settings=imagesEnabled=false")
        
        # Disable JavaScript (not recommended for Facebook)
        if stealth_config.disable_javascript:
            args.append("--disable-javascript")
        
        # Notifications
        if stealth_config.disable_notifications:
            args.append("--disable-notifications")
        
        # Popup blocking
        if stealth_config.disable_popup_blocking:
            args.append("--disable-popup-blocking")
        
        # Web security (dangerous, use with caution)
        if stealth_config.disable_web_security:
            args.extend([
                "--disable-web-security",
                "--disable-site-isolation-trials"
            ])
        
        # Additional custom arguments
        if stealth_config.extra_browser_args:
            args.extend(stealth_config.extra_browser_args)
        
        return args
    
    def _build_proxy_url(self, proxy_config: ProxyConfig) -> str:
        """Build proxy URL from configuration"""
        if proxy_config.proxy_type == 'none':
            return ""
        
        protocol = proxy_config.proxy_type
        if protocol == 'https':
            protocol = 'http'  # Chrome uses http for HTTPS proxies
        
        if proxy_config.username and proxy_config.password:
            return f"{protocol}://{proxy_config.username}:{proxy_config.password}@{proxy_config.host}:{proxy_config.port}"
        else:
            return f"{protocol}://{proxy_config.host}:{proxy_config.port}"
    
    async def _apply_stealth_configurations(
        self, 
        browser: zd.Browser, 
        profile: AntidetectProfile,
        stealth_config: StealthBrowserConfig
    ):
        """Apply additional stealth configurations after browser creation"""
        try:
            tab = browser.main_tab
            
            # Set viewport size
            if profile.viewport_size:
                await tab.send(zd.cdp.emulation.set_device_metrics_override(
                    width=profile.viewport_size[0],
                    height=profile.viewport_size[1],
                    device_scale_factor=1.0,
                    mobile=False
                ))
            
            # Set timezone
            if profile.timezone:
                await tab.send(zd.cdp.emulation.set_timezone_override(
                    timezone_id=profile.timezone
                ))
            
            # Set locale
            if profile.locale:
                await tab.send(zd.cdp.emulation.set_locale_override(
                    locale=profile.locale
                ))
            
            # WebGL fingerprint spoofing
            if profile.webgl_vendor or profile.webgl_renderer:
                webgl_script = f"""
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                    if (parameter === 37445) return '{profile.webgl_vendor or 'Intel Inc.'}';
                    if (parameter === 37446) return '{profile.webgl_renderer or 'Intel Iris OpenGL Engine'}';
                    return getParameter.call(this, parameter);
                }};
                """
                await tab.evaluate(webgl_script)
            
            # Canvas fingerprint protection
            if profile.canvas_fingerprint:
                canvas_script = """
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function() {
                    const context = this.getContext('2d');
                    if (context) {
                        context.fillStyle = 'rgba(255, 255, 255, 0.01)';
                        context.fillRect(0, 0, 1, 1);
                    }
                    return originalToDataURL.apply(this, arguments);
                };
                """
                await tab.evaluate(canvas_script)
            
            logger.debug(f"Applied stealth configurations for profile: {profile.profile_name}")
            
        except Exception as e:
            logger.warning(f"Failed to apply some stealth configurations: {e}")
    
    async def get_browser(self, profile_id: str) -> Optional[zd.Browser]:
        """Get active browser by profile ID"""
        return self.active_browsers.get(profile_id)
    
    async def close_browser(self, profile_id: str) -> bool:
        """Close browser for specific profile"""
        try:
            if profile_id in self.active_browsers:
                browser = self.active_browsers[profile_id]
                await browser.stop()
                del self.active_browsers[profile_id]
                del self.profile_configs[profile_id]
                logger.info(f"Closed browser for profile: {profile_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to close browser for profile {profile_id}: {e}")
            return False
    
    async def close_all_browsers(self):
        """Close all active browsers"""
        for profile_id in list(self.active_browsers.keys()):
            await self.close_browser(profile_id)
    
    def get_active_profiles(self) -> List[str]:
        """Get list of active profile IDs"""
        return list(self.active_browsers.keys())
    
    async def is_browser_healthy(self, profile_id: str) -> bool:
        """Check if browser is healthy and responsive"""
        try:
            browser = self.active_browsers.get(profile_id)
            if not browser:
                return False
            
            # Try to get current URL as health check
            tab = browser.main_tab
            await tab.evaluate("window.location.href")
            return True
            
        except Exception:
            return False


# Factory functions for common configurations
def create_facebook_profile(
    profile_id: str,
    profile_name: str,
    user_data_dir: str,
    proxy_config: ProxyConfig = None
) -> AntidetectProfile:
    """Create antidetect profile optimized for Facebook"""
    
    if proxy_config is None:
        proxy_config = ProxyConfig(proxy_type='none')
    
    return AntidetectProfile(
        profile_id=profile_id,
        profile_name=profile_name,
        user_data_dir=user_data_dir,
        proxy_config=proxy_config,
        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        viewport_size=(1920, 1080),
        timezone="America/New_York",
        locale="en-US"
    )


def create_stealth_config(
    headless: bool = False,
    performance_mode: bool = False
) -> StealthBrowserConfig:
    """Create stealth configuration for Facebook automation"""
    
    extra_args = []
    if performance_mode:
        extra_args.extend([
            "--disable-gpu",
            "--disable-software-rasterizer",
            "--disable-background-timer-throttling"
        ])
    
    return StealthBrowserConfig(
        headless=headless,
        off_screen_position=(-2000, -2000),
        window_size=(1920, 1080),
        disable_images=performance_mode,
        disable_notifications=True,
        disable_popup_blocking=True,
        extra_browser_args=extra_args
    )
