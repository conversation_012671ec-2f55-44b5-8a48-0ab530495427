"""
Facebook Comment API Interceptor using Zendriver with CDP Network Monitoring
Implements Visible Browser with Off-screen positioning for optimal stealth
"""
import asyncio
import json
import re
import time
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass, asdict
from urllib.parse import urlparse, parse_qs
import sys
import os

# Add zendriver_local to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'zendriver_local'))

import zendriver as zd
from zendriver import cdp
from loguru import logger


@dataclass
class FacebookComment:
    """Facebook comment data structure"""
    uid: str
    username: str
    comment_text: str
    comment_id: str
    created_time: int
    profile_url: Optional[str] = None
    is_anonymous: bool = False
    reply_count: int = 0
    reaction_count: int = 0


@dataclass
class CommentExtractionResult:
    """Result of comment extraction"""
    comments: List[FacebookComment]
    total_extracted: int
    processing_time: float
    post_url: str
    extraction_method: str = "api_interception"
    metadata: Dict[str, Any] = None


@dataclass
class InterceptionConfig:
    """Configuration for API interception"""
    headless: bool = False
    off_screen_position: tuple = (-2000, -2000)
    window_size: tuple = (1920, 1080)
    max_scroll_attempts: int = 10
    scroll_delay: float = 2.0
    api_timeout: float = 30.0
    max_comments: int = 1000


class FacebookCommentAPIInterceptor:
    """
    Facebook Comment API Interceptor using Zendriver with CDP Network Monitoring
    Implements Visible Browser with Off-screen positioning
    """
    
    def __init__(self, config: InterceptionConfig, profile_config: Dict[str, Any] = None):
        self.config = config
        self.profile_config = profile_config or {}
        self.browser = None
        self.tab = None
        self.intercepted_responses: List[Dict[str, Any]] = []
        self.comment_api_pattern = re.compile(r'facebook\.com/api/graphql/')
        self.comment_friendly_names = [
            'CommentsListComponentsPaginationQuery',
            'CommentListComponentsRootQuery',
            'UFICommentsProviderPaginationQuery'
        ]
        
    async def initialize(self) -> bool:
        """Initialize browser with off-screen positioning"""
        try:
            # Create browser config with off-screen positioning
            browser_config = zd.Config(
                headless=self.config.headless,
                no_sandbox=True,  # Fix for browser connection issues
                browser_args=[
                    f"--window-position={self.config.off_screen_position[0]},{self.config.off_screen_position[1]}",
                    f"--window-size={self.config.window_size[0]},{self.config.window_size[1]}",
                    "--disable-notifications",
                    "--disable-popup-blocking",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--no-sandbox",  # Additional sandbox fix
                    "--disable-setuid-sandbox"
                ]
            )
            
            # Add profile-specific configurations
            if self.profile_config.get('user_data_dir'):
                browser_config.user_data_dir = self.profile_config['user_data_dir']
            
            if self.profile_config.get('proxy'):
                proxy_config = self.profile_config['proxy']
                if proxy_config.get('host') and proxy_config.get('port'):
                    proxy_arg = f"--proxy-server={proxy_config['host']}:{proxy_config['port']}"
                    browser_config.browser_args.append(proxy_arg)
            
            # Start browser
            self.browser = await zd.start(config=browser_config)
            self.tab = self.browser.main_tab

            # Load saved cookies if available
            await self._load_saved_cookies()

            # Setup network monitoring
            await self._setup_network_monitoring()
            
            logger.info("FacebookCommentAPIInterceptor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize interceptor: {e}")
            return False

    async def _load_saved_cookies(self):
        """Load saved Facebook cookies into browser"""
        try:
            if not self.profile_config or not self.profile_config.get('user_data_dir'):
                logger.info("No profile directory specified, skipping cookie loading")
                return

            user_data_dir = self.profile_config['user_data_dir']

            # Check for Chrome/Chromium cookies database
            cookies_db_path = os.path.join(user_data_dir, "Default", "Cookies")

            if os.path.exists(cookies_db_path):
                logger.info(f"Found Chrome cookies database at: {cookies_db_path}")
                # Browser will automatically load cookies from user_data_dir
                # No additional action needed as we already set user_data_dir in browser config
                logger.info("✅ Browser will automatically load cookies from profile directory")
                return

            # Check for zendriver session file (.session.dat)
            session_file = os.path.join(user_data_dir, ".session.dat")

            if os.path.exists(session_file):
                logger.info(f"Found zendriver session file at: {session_file}")
                try:
                    # Load cookies using zendriver's cookie API
                    await self.browser.cookies.load(session_file, pattern="facebook|fb")
                    logger.info("✅ Loaded Facebook cookies from zendriver session file")
                    return
                except Exception as e:
                    logger.warning(f"Failed to load cookies from session file: {e}")

            # Check for custom Facebook cookies JSON file
            fb_cookies_file = os.path.join(user_data_dir, "facebook_cookies.json")

            if os.path.exists(fb_cookies_file):
                logger.info(f"Found Facebook cookies file at: {fb_cookies_file}")
                try:
                    await self._load_facebook_cookies_from_json(fb_cookies_file)
                    logger.info("✅ Loaded Facebook cookies from JSON file")
                    return
                except Exception as e:
                    logger.warning(f"Failed to load cookies from JSON file: {e}")

            logger.info("No saved cookies found - browser will start with clean session")

        except Exception as e:
            logger.warning(f"Error loading saved cookies: {e}")

    async def _load_facebook_cookies_from_json(self, cookies_file: str):
        """Load Facebook cookies from JSON file"""
        import json

        try:
            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)

            # Convert to zendriver cookie format
            zendriver_cookies = []

            for cookie in cookies_data:
                # Handle different cookie formats
                if isinstance(cookie, dict):
                    zendriver_cookie = {
                        'name': cookie.get('name', ''),
                        'value': cookie.get('value', ''),
                        'domain': cookie.get('domain', '.facebook.com'),
                        'path': cookie.get('path', '/'),
                        'secure': cookie.get('secure', True),
                        'httpOnly': cookie.get('httpOnly', False)
                    }

                    # Add expiration if available
                    if 'expires' in cookie:
                        zendriver_cookie['expires'] = cookie['expires']
                    elif 'expirationDate' in cookie:
                        zendriver_cookie['expires'] = cookie['expirationDate']

                    zendriver_cookies.append(zendriver_cookie)

            if zendriver_cookies:
                # Set cookies in browser
                await self.browser.cookies.set_all(zendriver_cookies)
                logger.info(f"Loaded {len(zendriver_cookies)} Facebook cookies")
            else:
                logger.warning("No valid cookies found in JSON file")

        except Exception as e:
            logger.error(f"Error loading cookies from JSON: {e}")
            raise

    async def _check_facebook_login_status(self):
        """Check if user is logged into Facebook"""
        try:
            logger.info("Checking Facebook login status...")

            # Navigate to Facebook home page first
            await self.tab.get("https://www.facebook.com/")
            await self.tab.wait_for_ready_state(until="complete")
            await asyncio.sleep(2)

            # Check current URL and page content for login indicators
            current_url = await self.tab.evaluate("window.location.href")

            # Check for login page indicators
            if "login" in current_url.lower():
                logger.warning("⚠️ Not logged into Facebook - redirected to login page")
                logger.info("💡 Tip: Use the 'Facebook Login' button in Profiles page to login first")
                return False

            # Check for common logged-in elements
            try:
                # Look for user navigation elements that indicate logged-in state
                logged_in_indicators = [
                    '[data-testid="blue_bar_profile_link"]',  # Profile link in top bar
                    '[aria-label="Account"]',  # Account menu
                    '[data-testid="left_nav_menu_item"]',  # Left navigation menu
                    'div[role="banner"]',  # Facebook header banner
                ]

                for selector in logged_in_indicators:
                    try:
                        element = await self.tab.find_element(selector, timeout=2)
                        if element:
                            logger.info("✅ Facebook login verified - found logged-in indicators")
                            return True
                    except:
                        continue

                # If no indicators found, check page title
                page_title = await self.tab.evaluate("document.title")
                if "Facebook" in page_title and "Log In" not in page_title:
                    logger.info("✅ Facebook login verified - on Facebook homepage")
                    return True

                logger.warning("⚠️ Facebook login status unclear - proceeding with caution")
                return True  # Proceed anyway, might still work

            except Exception as e:
                logger.warning(f"Could not verify login status: {e}")
                return True  # Proceed anyway

        except Exception as e:
            logger.error(f"Error checking Facebook login status: {e}")
            return True  # Proceed anyway

    async def _verify_post_access(self):
        """Verify that we can access the Facebook post"""
        try:
            current_url = await self.tab.evaluate("window.location.href")

            # Check if redirected to login
            if "login" in current_url.lower():
                logger.warning("⚠️ Post access blocked - redirected to login page")
                logger.info("💡 This post may require login to view comments")
                return False

            # Check for error messages
            error_selectors = [
                '[data-testid="error_message"]',
                '.error',
                '[role="alert"]'
            ]

            for selector in error_selectors:
                try:
                    error_element = await self.tab.find_element(selector, timeout=1)
                    if error_element:
                        error_text = await error_element.text
                        logger.warning(f"⚠️ Post access issue: {error_text}")
                        return False
                except:
                    continue

            # Check page title for error indicators
            page_title = await self.tab.evaluate("document.title")
            if any(error_word in page_title.lower() for error_word in ['error', 'not found', 'unavailable']):
                logger.warning(f"⚠️ Post may not be accessible: {page_title}")
                return False

            logger.info("✅ Post access verified - proceeding with comment extraction")
            return True

        except Exception as e:
            logger.warning(f"Could not verify post access: {e}")
            return True  # Proceed anyway

    async def _setup_network_monitoring(self):
        """Setup selective network monitoring for Facebook comment APIs"""
        try:
            # Enable network domain
            await self.tab.send(cdp.network.enable())
            
            # Add handlers for network events
            self.tab.add_handler(cdp.network.RequestWillBeSent, self._handle_request)
            self.tab.add_handler(cdp.network.ResponseReceived, self._handle_response)
            
            logger.info("Network monitoring setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup network monitoring: {e}")
            raise
    
    async def _handle_request(self, event: cdp.network.RequestWillBeSent):
        """Handle outgoing requests - filter for Facebook GraphQL"""
        request = event.request
        
        # Check if this is a Facebook GraphQL request
        if self.comment_api_pattern.search(request.url):
            friendly_name = request.headers.get('x-fb-friendly-name', '')
            
            if any(name in friendly_name for name in self.comment_friendly_names):
                logger.debug(f"Intercepted comment API request: {friendly_name}")
                # Store request info for correlation with response
                self.intercepted_responses.append({
                    'request_id': event.request_id,
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'friendly_name': friendly_name,
                    'timestamp': time.time()
                })
    
    async def _handle_response(self, event: cdp.network.ResponseReceived):
        """Handle incoming responses - capture comment API responses"""
        response = event.response
        request_id = event.request_id
        
        # Find matching request
        matching_request = None
        for req in self.intercepted_responses:
            if req['request_id'] == request_id:
                matching_request = req
                break
        
        if matching_request and response.status == 200:
            try:
                # Get response body
                response_body = await self.tab.send(cdp.network.get_response_body(request_id))
                
                if response_body and response_body.body:
                    # Parse JSON response
                    json_data = json.loads(response_body.body)
                    
                    # Store complete response data
                    matching_request.update({
                        'response_status': response.status,
                        'response_headers': dict(response.headers),
                        'response_body': json_data,
                        'response_timestamp': time.time()
                    })
                    
                    logger.debug(f"Captured comment API response: {matching_request['friendly_name']}")
                    
            except Exception as e:
                logger.warning(f"Failed to capture response body for {request_id}: {e}")
    
    async def extract_comments(self, post_url: str) -> CommentExtractionResult:
        """
        Extract comments from Facebook post using API interception
        """
        start_time = time.time()
        
        try:
            # First check Facebook login status
            await self._check_facebook_login_status()

            # Navigate to post
            logger.info(f"Navigating to Facebook post: {post_url}")
            await self.tab.get(post_url)
            await self.tab.wait_for_ready_state(until="complete")

            # Wait for initial page load
            await asyncio.sleep(3)

            # Check if we can access the post (not blocked by login)
            await self._verify_post_access()
            
            # Clear previous intercepted responses
            self.intercepted_responses.clear()
            
            # Perform smart scrolling to trigger comment loading
            comments = await self._smart_scroll_and_extract()
            
            processing_time = time.time() - start_time
            
            result = CommentExtractionResult(
                comments=comments,
                total_extracted=len(comments),
                processing_time=processing_time,
                post_url=post_url,
                metadata={
                    'api_calls_intercepted': len(self.intercepted_responses),
                    'scroll_attempts': self.config.max_scroll_attempts,
                    'extraction_timestamp': time.time()
                }
            )
            
            logger.info(f"Extracted {len(comments)} comments in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Failed to extract comments: {e}")
            raise
    
    async def _smart_scroll_and_extract(self) -> List[FacebookComment]:
        """Smart scrolling strategy to trigger comment API loading"""
        all_comments = []
        scroll_attempts = 0
        
        while scroll_attempts < self.config.max_scroll_attempts and len(all_comments) < self.config.max_comments:
            # Scroll down to trigger more comment loading
            await self.tab.scroll_down(500)
            
            # Wait for API calls to complete
            await asyncio.sleep(self.config.scroll_delay)
            
            # Try to click "View more comments" if available
            try:
                view_more_btn = await self.tab.find("View more comments", best_match=True)
                if view_more_btn:
                    await view_more_btn.click()
                    await asyncio.sleep(2)
            except:
                pass  # Button not found or not clickable
            
            # Extract comments from intercepted API responses
            new_comments = self._extract_from_intercepted_responses()
            
            if new_comments:
                # Deduplicate comments
                existing_ids = {c.comment_id for c in all_comments}
                unique_new_comments = [c for c in new_comments if c.comment_id not in existing_ids]
                all_comments.extend(unique_new_comments)
                
                logger.debug(f"Scroll {scroll_attempts + 1}: Found {len(unique_new_comments)} new comments")
            
            scroll_attempts += 1
            
            # Break if no new comments found in last few attempts
            if scroll_attempts > 3 and not new_comments:
                break
        
        return all_comments
    
    def _extract_from_intercepted_responses(self) -> List[FacebookComment]:
        """Extract comments from intercepted API responses"""
        comments = []

        for response_data in self.intercepted_responses:
            if 'response_body' not in response_data:
                continue

            try:
                json_data = response_data['response_body']

                # Navigate through Facebook's GraphQL response structure
                if 'data' in json_data and 'node' in json_data['data']:
                    node = json_data['data']['node']

                    # Look for comment rendering instance
                    if 'comment_rendering_instance_for_feed_location' in node:
                        comment_instance = node['comment_rendering_instance_for_feed_location']

                        if 'comments' in comment_instance and 'edges' in comment_instance['comments']:
                            edges = comment_instance['comments']['edges']

                            for edge in edges:
                                if 'node' in edge:
                                    comment_node = edge['node']
                                    comment = self._parse_comment_node(comment_node)
                                    if comment:
                                        comments.append(comment)

            except Exception as e:
                logger.warning(f"Failed to parse comment response: {e}")
                continue

        return comments

    def _parse_comment_node(self, comment_node: Dict[str, Any]) -> Optional[FacebookComment]:
        """Parse individual comment node from Facebook API response"""
        try:
            # Extract basic comment info
            comment_id = comment_node.get('id', '')
            legacy_fbid = comment_node.get('legacy_fbid', '')

            # Extract comment text
            comment_text = ''
            if 'body' in comment_node and 'text' in comment_node['body']:
                comment_text = comment_node['body']['text']
            elif 'preferred_body' in comment_node and 'text' in comment_node['preferred_body']:
                comment_text = comment_node['preferred_body']['text']

            # Extract author information
            author = comment_node.get('author', {})
            username = author.get('name', 'Unknown')
            uid = author.get('id', '')

            # Handle anonymous authors
            is_anonymous = author.get('__typename') == 'GroupAnonAuthorProfile'

            # Extract profile URL if available
            profile_url = author.get('url')

            # Extract timestamps
            created_time = comment_node.get('created_time', 0)

            # Extract engagement metrics
            feedback = comment_node.get('feedback', {})
            reaction_count = 0
            if 'reactors' in feedback:
                reaction_count = feedback['reactors'].get('count', 0)

            reply_count = 0
            if 'replies_fields' in feedback:
                reply_count = feedback['replies_fields'].get('count', 0)

            return FacebookComment(
                uid=uid,
                username=username,
                comment_text=comment_text,
                comment_id=legacy_fbid or comment_id,
                created_time=created_time,
                profile_url=profile_url,
                is_anonymous=is_anonymous,
                reply_count=reply_count,
                reaction_count=reaction_count
            )

        except Exception as e:
            logger.warning(f"Failed to parse comment node: {e}")
            return None

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.browser:
                await self.browser.stop()
                logger.info("Browser cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Factory function for easy instantiation
async def create_facebook_comment_interceptor(
    profile_config: Dict[str, Any] = None,
    max_comments: int = 1000,
    headless: bool = False
) -> FacebookCommentAPIInterceptor:
    """
    Factory function to create and initialize FacebookCommentAPIInterceptor

    Args:
        profile_config: Antidetect profile configuration
        max_comments: Maximum number of comments to extract
        headless: Whether to run in headless mode (not recommended for Facebook)

    Returns:
        Initialized FacebookCommentAPIInterceptor instance
    """
    config = InterceptionConfig(
        headless=headless,
        max_comments=max_comments
    )

    interceptor = FacebookCommentAPIInterceptor(config, profile_config)

    if await interceptor.initialize():
        return interceptor
    else:
        raise Exception("Failed to initialize FacebookCommentAPIInterceptor")


# Example usage
async def example_usage():
    """Example of how to use FacebookCommentAPIInterceptor"""

    # Profile configuration (antidetect browser settings)
    profile_config = {
        'user_data_dir': '/path/to/profile/data',
        'proxy': {
            'host': '127.0.0.1',
            'port': '8080',
            'username': 'user',
            'password': 'pass'
        }
    }

    try:
        # Create interceptor
        interceptor = await create_facebook_comment_interceptor(
            profile_config=profile_config,
            max_comments=500,
            headless=False  # Visible browser for better stealth
        )

        # Extract comments from Facebook post
        post_url = "https://www.facebook.com/groups/example/posts/123456789/"
        result = await interceptor.extract_comments(post_url)

        # Process results
        print(f"Extracted {result.total_extracted} comments in {result.processing_time:.2f}s")

        for comment in result.comments:
            print(f"UID: {comment.uid}, User: {comment.username}")
            print(f"Comment: {comment.comment_text[:100]}...")
            print("---")

    except Exception as e:
        logger.error(f"Error in example usage: {e}")

    finally:
        # Cleanup
        if 'interceptor' in locals():
            await interceptor.cleanup()


if __name__ == "__main__":
    # Run example
    asyncio.run(example_usage())
