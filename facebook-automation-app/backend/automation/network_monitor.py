"""
Advanced Network Monitor for Facebook GraphQL API Interception
Selective monitoring with intelligent filtering for comment APIs
"""
import asyncio
import json
import re
import time
from typing import Dict, List, Any, Optional, Callable, Pattern
from dataclasses import dataclass, field
from urllib.parse import urlparse, parse_qs
import sys
import os

# Add zendriver_local to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'zendriver_local'))

from zendriver import cdp
from loguru import logger


@dataclass
class NetworkRequest:
    """Network request data structure"""
    request_id: str
    url: str
    method: str
    headers: Dict[str, str]
    post_data: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    friendly_name: Optional[str] = None


@dataclass
class NetworkResponse:
    """Network response data structure"""
    request_id: str
    status: int
    headers: Dict[str, str]
    body: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    processing_time: Optional[float] = None


@dataclass
class APICall:
    """Complete API call (request + response)"""
    request: NetworkRequest
    response: Optional[NetworkResponse] = None
    is_complete: bool = False
    api_type: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None


@dataclass
class MonitoringConfig:
    """Configuration for network monitoring"""
    enable_request_logging: bool = True
    enable_response_logging: bool = True
    enable_body_capture: bool = True
    max_body_size: int = 10 * 1024 * 1024  # 10MB
    request_timeout: float = 30.0
    buffer_size: int = 1000
    auto_cleanup_interval: float = 300.0  # 5 minutes


class FacebookAPIFilter:
    """Intelligent filter for Facebook GraphQL APIs"""
    
    def __init__(self):
        # Facebook GraphQL endpoint patterns
        self.graphql_patterns = [
            re.compile(r'facebook\.com/api/graphql/?'),
            re.compile(r'facebook\.com/ajax/'),
            re.compile(r'facebook\.com/api/'),
        ]
        
        # Comment-related API friendly names
        self.comment_api_names = [
            'CommentsListComponentsPaginationQuery',
            'CommentListComponentsRootQuery',
            'UFICommentsProviderPaginationQuery',
            'CometUFICommentsProviderQuery',
            'CommentListComponentsQuery',
            'UFIRepliesExpansionPaginationQuery',
            'CometModernHomeFeedQuery',
            'GroupsCometFeedRegularStoriesQuery'
        ]
        
        # Post-related API friendly names
        self.post_api_names = [
            'CometSinglePostContentQuery',
            'GroupsCometPermalinkRootQuery',
            'CometGroupDiscussionRootSuccessQuery'
        ]
        
        # User interaction API names
        self.interaction_api_names = [
            'CometUFIReactMutation',
            'UFILikeButtonMutation',
            'CometUFICommentCreateMutation'
        ]
    
    def is_facebook_graphql(self, url: str) -> bool:
        """Check if URL is Facebook GraphQL endpoint"""
        return any(pattern.search(url) for pattern in self.graphql_patterns)
    
    def get_api_type(self, request: NetworkRequest) -> Optional[str]:
        """Determine API type based on friendly name"""
        friendly_name = request.friendly_name or ''
        
        if any(name in friendly_name for name in self.comment_api_names):
            return 'comment'
        elif any(name in friendly_name for name in self.post_api_names):
            return 'post'
        elif any(name in friendly_name for name in self.interaction_api_names):
            return 'interaction'
        
        return None
    
    def should_monitor(self, request: NetworkRequest) -> bool:
        """Determine if request should be monitored"""
        if not self.is_facebook_graphql(request.url):
            return False
        
        # Check for comment-related APIs
        api_type = self.get_api_type(request)
        return api_type in ['comment', 'post']
    
    def extract_request_params(self, request: NetworkRequest) -> Dict[str, Any]:
        """Extract useful parameters from request"""
        params = {}
        
        try:
            # Parse URL parameters
            parsed_url = urlparse(request.url)
            url_params = parse_qs(parsed_url.query)
            
            # Extract doc_id (GraphQL query identifier)
            if 'doc_id' in url_params:
                params['doc_id'] = url_params['doc_id'][0]
            
            # Parse POST data if available
            if request.post_data:
                # Facebook uses form-encoded data
                post_params = parse_qs(request.post_data)
                
                # Extract variables (GraphQL variables)
                if 'variables' in post_params:
                    try:
                        variables = json.loads(post_params['variables'][0])
                        params['variables'] = variables
                    except:
                        pass
                
                # Extract other useful parameters
                for key in ['fb_dtsg', 'jazoest', '__user', '__a']:
                    if key in post_params:
                        params[key] = post_params[key][0]
            
        except Exception as e:
            logger.debug(f"Failed to extract request params: {e}")
        
        return params


class NetworkMonitor:
    """
    Advanced network monitor for Facebook API interception
    Provides selective monitoring with intelligent filtering
    """
    
    def __init__(self, config: MonitoringConfig = None):
        self.config = config or MonitoringConfig()
        self.filter = FacebookAPIFilter()
        
        # Storage for API calls
        self.active_requests: Dict[str, NetworkRequest] = {}
        self.completed_calls: List[APICall] = []
        self.api_call_handlers: List[Callable[[APICall], None]] = []
        
        # Monitoring state
        self.is_monitoring = False
        self.tab = None
        self.cleanup_task = None
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'monitored_requests': 0,
            'completed_calls': 0,
            'failed_calls': 0,
            'comment_apis': 0,
            'post_apis': 0
        }
    
    async def start_monitoring(self, tab) -> bool:
        """Start network monitoring on given tab"""
        try:
            self.tab = tab
            
            # Enable network domain
            await tab.send(cdp.network.enable(
                max_total_buffer_size=self.config.buffer_size * 1024,
                max_resource_buffer_size=self.config.max_body_size
            ))
            
            # Add event handlers
            tab.add_handler(cdp.network.RequestWillBeSent, self._handle_request)
            tab.add_handler(cdp.network.ResponseReceived, self._handle_response)
            tab.add_handler(cdp.network.LoadingFinished, self._handle_loading_finished)
            tab.add_handler(cdp.network.LoadingFailed, self._handle_loading_failed)
            
            self.is_monitoring = True
            
            # Start cleanup task
            if self.config.auto_cleanup_interval > 0:
                self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info("Network monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start network monitoring: {e}")
            return False
    
    async def stop_monitoring(self):
        """Stop network monitoring"""
        try:
            self.is_monitoring = False
            
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            if self.tab:
                await self.tab.send(cdp.network.disable())
            
            logger.info("Network monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping network monitoring: {e}")
    
    async def _handle_request(self, event: cdp.network.RequestWillBeSent):
        """Handle outgoing network requests"""
        try:
            request = NetworkRequest(
                request_id=event.request_id,
                url=event.request.url,
                method=event.request.method,
                headers=dict(event.request.headers),
                post_data=event.request.post_data,
                friendly_name=event.request.headers.get('x-fb-friendly-name')
            )
            
            self.stats['total_requests'] += 1
            
            # Apply filtering
            if self.filter.should_monitor(request):
                self.active_requests[event.request_id] = request
                self.stats['monitored_requests'] += 1
                
                # Determine API type
                api_type = self.filter.get_api_type(request)
                if api_type == 'comment':
                    self.stats['comment_apis'] += 1
                elif api_type == 'post':
                    self.stats['post_apis'] += 1
                
                logger.debug(f"Monitoring {api_type} API: {request.friendly_name}")
            
        except Exception as e:
            logger.warning(f"Error handling request: {e}")
    
    async def _handle_response(self, event: cdp.network.ResponseReceived):
        """Handle incoming network responses"""
        try:
            request_id = event.request_id
            
            if request_id in self.active_requests:
                response = NetworkResponse(
                    request_id=request_id,
                    status=event.response.status,
                    headers=dict(event.response.headers)
                )
                
                # Create API call object
                request = self.active_requests[request_id]
                api_call = APICall(
                    request=request,
                    response=response,
                    api_type=self.filter.get_api_type(request)
                )
                
                # Store for body capture
                self.active_requests[request_id] = api_call
                
        except Exception as e:
            logger.warning(f"Error handling response: {e}")
    
    async def _handle_loading_finished(self, event: cdp.network.LoadingFinished):
        """Handle completed network requests"""
        try:
            request_id = event.request_id
            
            if request_id in self.active_requests:
                api_call = self.active_requests[request_id]
                
                if isinstance(api_call, APICall) and api_call.response:
                    # Capture response body if enabled
                    if self.config.enable_body_capture:
                        try:
                            body_result = await self.tab.send(
                                cdp.network.get_response_body(request_id)
                            )
                            
                            if body_result and body_result.body:
                                api_call.response.body = body_result.body
                                
                                # Try to parse JSON response
                                if api_call.response.headers.get('content-type', '').startswith('application/json'):
                                    try:
                                        api_call.extracted_data = json.loads(body_result.body)
                                    except:
                                        pass
                        
                        except Exception as e:
                            logger.debug(f"Failed to capture response body: {e}")
                    
                    # Mark as complete
                    api_call.is_complete = True
                    api_call.response.processing_time = time.time() - api_call.request.timestamp
                    
                    # Move to completed calls
                    self.completed_calls.append(api_call)
                    del self.active_requests[request_id]
                    
                    self.stats['completed_calls'] += 1
                    
                    # Notify handlers
                    for handler in self.api_call_handlers:
                        try:
                            handler(api_call)
                        except Exception as e:
                            logger.warning(f"Error in API call handler: {e}")
                    
                    logger.debug(f"Completed {api_call.api_type} API call: {api_call.request.friendly_name}")
        
        except Exception as e:
            logger.warning(f"Error handling loading finished: {e}")
    
    async def _handle_loading_failed(self, event: cdp.network.LoadingFailed):
        """Handle failed network requests"""
        try:
            request_id = event.request_id
            
            if request_id in self.active_requests:
                del self.active_requests[request_id]
                self.stats['failed_calls'] += 1
                
                logger.debug(f"Network request failed: {event.error_text}")
        
        except Exception as e:
            logger.warning(f"Error handling loading failed: {e}")
    
    def add_api_call_handler(self, handler: Callable[[APICall], None]):
        """Add handler for completed API calls"""
        self.api_call_handlers.append(handler)
    
    def remove_api_call_handler(self, handler: Callable[[APICall], None]):
        """Remove API call handler"""
        if handler in self.api_call_handlers:
            self.api_call_handlers.remove(handler)
    
    def get_completed_calls(self, api_type: str = None) -> List[APICall]:
        """Get completed API calls, optionally filtered by type"""
        if api_type:
            return [call for call in self.completed_calls if call.api_type == api_type]
        return self.completed_calls.copy()
    
    def clear_completed_calls(self):
        """Clear completed API calls to free memory"""
        self.completed_calls.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        return {
            **self.stats,
            'active_requests': len(self.active_requests),
            'completed_calls_stored': len(self.completed_calls),
            'is_monitoring': self.is_monitoring
        }
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old data"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.config.auto_cleanup_interval)
                
                # Remove old completed calls (keep last 100)
                if len(self.completed_calls) > 100:
                    self.completed_calls = self.completed_calls[-100:]
                
                # Remove stale active requests (older than timeout)
                current_time = time.time()
                stale_requests = [
                    req_id for req_id, req in self.active_requests.items()
                    if isinstance(req, NetworkRequest) and 
                    current_time - req.timestamp > self.config.request_timeout
                ]
                
                for req_id in stale_requests:
                    del self.active_requests[req_id]
                    self.stats['failed_calls'] += 1
                
                if stale_requests:
                    logger.debug(f"Cleaned up {len(stale_requests)} stale requests")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Error in cleanup loop: {e}")


# Factory function
def create_facebook_network_monitor(
    enable_body_capture: bool = True,
    buffer_size: int = 1000
) -> NetworkMonitor:
    """Create network monitor optimized for Facebook API interception"""
    
    config = MonitoringConfig(
        enable_body_capture=enable_body_capture,
        buffer_size=buffer_size,
        max_body_size=5 * 1024 * 1024,  # 5MB for Facebook responses
        request_timeout=60.0,  # Facebook can be slow
        auto_cleanup_interval=300.0
    )
    
    return NetworkMonitor(config)
