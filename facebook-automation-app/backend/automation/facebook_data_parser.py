"""
Facebook Data Parser for GraphQL API Responses
Handles comment extraction, pagination, and UID parsing from Facebook API responses
"""
import json
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from urllib.parse import urlparse
import time
from loguru import logger


@dataclass
class FacebookUser:
    """Facebook user data structure"""
    uid: str
    username: str
    profile_url: Optional[str] = None
    is_anonymous: bool = False
    profile_picture_url: Optional[str] = None
    gender: Optional[str] = None
    is_verified: bool = False


@dataclass
class FacebookComment:
    """Facebook comment data structure"""
    comment_id: str
    legacy_fbid: str
    author: FacebookUser
    text: str
    created_time: int
    reply_count: int = 0
    reaction_count: int = 0
    parent_comment_id: Optional[str] = None
    is_edited: bool = False
    attachments: List[Dict[str, Any]] = field(default_factory=list)
    reactions: Dict[str, int] = field(default_factory=dict)


@dataclass
class PaginationInfo:
    """Pagination information from Facebook API"""
    has_next_page: bool
    has_previous_page: bool
    start_cursor: Optional[str] = None
    end_cursor: Optional[str] = None
    total_count: Optional[int] = None


@dataclass
class ExtractionResult:
    """Result of data extraction from API response"""
    comments: List[FacebookComment]
    users: List[FacebookUser]
    pagination: Optional[PaginationInfo] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    extraction_timestamp: float = field(default_factory=time.time)


class FacebookDataParser:
    """
    Parser for Facebook GraphQL API responses
    Extracts comments, users, and handles pagination
    """
    
    def __init__(self):
        # UID extraction patterns
        self.uid_patterns = [
            re.compile(r'facebook\.com/profile\.php\?id=(\d+)'),
            re.compile(r'facebook\.com/(\d{8,20})/?'),
            re.compile(r'"id":"(\d{8,20})"'),
            re.compile(r'user\.(\d{8,20})'),
            re.compile(r'/user/(\d{8,20})'),
        ]
        
        # Username cleaning patterns
        self.username_clean_patterns = [
            re.compile(r'[^\w\s\u00C0-\u017F\u1EA0-\u1EF9]'),  # Keep alphanumeric, spaces, and Vietnamese chars
        ]
        
        # Deduplication tracking
        self.seen_comment_ids: Set[str] = set()
        self.seen_user_ids: Set[str] = set()
    
    def parse_api_response(self, response_data: Dict[str, Any]) -> ExtractionResult:
        """
        Parse Facebook GraphQL API response
        
        Args:
            response_data: Raw JSON response from Facebook API
            
        Returns:
            ExtractionResult with extracted data
        """
        try:
            comments = []
            users = []
            pagination = None
            metadata = {}
            
            # Navigate through Facebook's GraphQL response structure
            if 'data' in response_data:
                data = response_data['data']
                
                # Handle different response structures
                if 'node' in data:
                    # Single node response (common for comment queries)
                    result = self._parse_node_response(data['node'])
                    comments.extend(result.comments)
                    users.extend(result.users)
                    pagination = result.pagination
                    metadata.update(result.metadata)
                
                elif 'viewer' in data:
                    # Viewer-based response
                    result = self._parse_viewer_response(data['viewer'])
                    comments.extend(result.comments)
                    users.extend(result.users)
                
                elif isinstance(data, list):
                    # Array response
                    for item in data:
                        if isinstance(item, dict):
                            result = self._parse_node_response(item)
                            comments.extend(result.comments)
                            users.extend(result.users)
            
            # Deduplicate results
            unique_comments = self._deduplicate_comments(comments)
            unique_users = self._deduplicate_users(users)
            
            # Extract additional metadata
            metadata.update({
                'total_comments_extracted': len(unique_comments),
                'total_users_extracted': len(unique_users),
                'response_size': len(str(response_data)),
                'has_pagination': pagination is not None
            })
            
            return ExtractionResult(
                comments=unique_comments,
                users=unique_users,
                pagination=pagination,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to parse API response: {e}")
            return ExtractionResult(comments=[], users=[], metadata={'error': str(e)})
    
    def _parse_node_response(self, node: Dict[str, Any]) -> ExtractionResult:
        """Parse node-based response structure"""
        comments = []
        users = []
        pagination = None
        metadata = {}
        
        try:
            # Look for comment rendering instance
            if 'comment_rendering_instance_for_feed_location' in node:
                comment_instance = node['comment_rendering_instance_for_feed_location']
                result = self._parse_comment_instance(comment_instance)
                comments.extend(result.comments)
                users.extend(result.users)
                pagination = result.pagination
            
            # Look for direct comments structure
            elif 'comments' in node:
                result = self._parse_comments_structure(node['comments'])
                comments.extend(result.comments)
                users.extend(result.users)
                pagination = result.pagination
            
            # Look for feedback structure
            elif 'feedback' in node:
                result = self._parse_feedback_structure(node['feedback'])
                comments.extend(result.comments)
                users.extend(result.users)
            
            # Extract post metadata if available
            if 'id' in node:
                metadata['post_id'] = node['id']
            if 'url' in node:
                metadata['post_url'] = node['url']
            
        except Exception as e:
            logger.warning(f"Error parsing node response: {e}")
        
        return ExtractionResult(comments=comments, users=users, pagination=pagination, metadata=metadata)
    
    def _parse_comment_instance(self, comment_instance: Dict[str, Any]) -> ExtractionResult:
        """Parse comment rendering instance"""
        comments = []
        users = []
        pagination = None
        
        if 'comments' in comment_instance:
            comments_data = comment_instance['comments']
            
            # Parse pagination info
            if 'page_info' in comments_data:
                pagination = self._parse_pagination_info(comments_data['page_info'])
            
            # Parse comment edges
            if 'edges' in comments_data:
                for edge in comments_data['edges']:
                    if 'node' in edge:
                        comment_result = self._parse_comment_node(edge['node'])
                        if comment_result:
                            comments.append(comment_result['comment'])
                            users.append(comment_result['user'])
        
        return ExtractionResult(comments=comments, users=users, pagination=pagination)
    
    def _parse_comments_structure(self, comments_data: Dict[str, Any]) -> ExtractionResult:
        """Parse direct comments structure"""
        comments = []
        users = []
        pagination = None
        
        # Parse pagination
        if 'page_info' in comments_data:
            pagination = self._parse_pagination_info(comments_data['page_info'])
        
        # Parse edges or nodes
        if 'edges' in comments_data:
            for edge in comments_data['edges']:
                if 'node' in edge:
                    comment_result = self._parse_comment_node(edge['node'])
                    if comment_result:
                        comments.append(comment_result['comment'])
                        users.append(comment_result['user'])
        elif 'nodes' in comments_data:
            for node in comments_data['nodes']:
                comment_result = self._parse_comment_node(node)
                if comment_result:
                    comments.append(comment_result['comment'])
                    users.append(comment_result['user'])
        
        return ExtractionResult(comments=comments, users=users, pagination=pagination)
    
    def _parse_feedback_structure(self, feedback: Dict[str, Any]) -> ExtractionResult:
        """Parse feedback structure for comments"""
        comments = []
        users = []
        
        # Look for comments in feedback
        if 'comments' in feedback:
            result = self._parse_comments_structure(feedback['comments'])
            comments.extend(result.comments)
            users.extend(result.users)
        
        # Look for replies
        if 'replies_connection' in feedback:
            result = self._parse_comments_structure(feedback['replies_connection'])
            comments.extend(result.comments)
            users.extend(result.users)
        
        return ExtractionResult(comments=comments, users=users)
    
    def _parse_comment_node(self, comment_node: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse individual comment node"""
        try:
            # Extract comment ID
            comment_id = comment_node.get('id', '')
            legacy_fbid = comment_node.get('legacy_fbid', '')
            
            if not comment_id and not legacy_fbid:
                return None
            
            # Extract comment text
            text = self._extract_comment_text(comment_node)
            
            # Extract author information
            author_data = comment_node.get('author', {})
            user = self._parse_user_data(author_data)
            
            if not user:
                return None
            
            # Extract timestamps
            created_time = comment_node.get('created_time', 0)
            
            # Extract engagement metrics
            reply_count = 0
            reaction_count = 0
            
            if 'feedback' in comment_node:
                feedback = comment_node['feedback']
                
                # Reply count
                if 'replies_fields' in feedback:
                    reply_count = feedback['replies_fields'].get('count', 0)
                
                # Reaction count
                if 'reactors' in feedback:
                    reaction_count = feedback['reactors'].get('count', 0)
                elif 'unified_reactors' in feedback:
                    reaction_count = feedback['unified_reactors'].get('count', 0)
            
            # Extract parent comment ID for replies
            parent_comment_id = None
            if 'comment_parent' in comment_node and comment_node['comment_parent']:
                parent_comment_id = comment_node['comment_parent'].get('id')
            
            # Extract attachments
            attachments = []
            if 'attachments' in comment_node:
                attachments = comment_node['attachments']
            
            # Create comment object
            comment = FacebookComment(
                comment_id=comment_id,
                legacy_fbid=legacy_fbid or comment_id,
                author=user,
                text=text,
                created_time=created_time,
                reply_count=reply_count,
                reaction_count=reaction_count,
                parent_comment_id=parent_comment_id,
                attachments=attachments
            )
            
            return {
                'comment': comment,
                'user': user
            }
            
        except Exception as e:
            logger.warning(f"Failed to parse comment node: {e}")
            return None
    
    def _extract_comment_text(self, comment_node: Dict[str, Any]) -> str:
        """Extract comment text from various possible locations"""
        text_sources = [
            ['body', 'text'],
            ['preferred_body', 'text'],
            ['body_renderer', 'text'],
            ['text'],
            ['message', 'text']
        ]
        
        for source_path in text_sources:
            try:
                current = comment_node
                for key in source_path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        break
                else:
                    if isinstance(current, str) and current.strip():
                        return current.strip()
            except:
                continue
        
        return ""
    
    def _parse_user_data(self, author_data: Dict[str, Any]) -> Optional[FacebookUser]:
        """Parse user data from author information"""
        try:
            # Extract user ID
            uid = author_data.get('id', '')
            if not uid:
                return None
            
            # Extract username
            username = author_data.get('name', 'Unknown')
            username = self._clean_username(username)
            
            # Extract profile URL
            profile_url = author_data.get('url')
            
            # Check if anonymous
            is_anonymous = author_data.get('__typename') == 'GroupAnonAuthorProfile'
            
            # Extract profile picture
            profile_picture_url = None
            if 'profile_picture_depth_0' in author_data:
                profile_picture_url = author_data['profile_picture_depth_0'].get('uri')
            elif 'profile_picture' in author_data:
                profile_picture_url = author_data['profile_picture'].get('uri')
            
            # Extract gender
            gender = author_data.get('gender')
            
            # Extract verification status
            is_verified = author_data.get('is_verified', False)
            
            return FacebookUser(
                uid=uid,
                username=username,
                profile_url=profile_url,
                is_anonymous=is_anonymous,
                profile_picture_url=profile_picture_url,
                gender=gender,
                is_verified=is_verified
            )
            
        except Exception as e:
            logger.warning(f"Failed to parse user data: {e}")
            return None
    
    def _clean_username(self, username: str) -> str:
        """Clean and normalize username"""
        if not username:
            return "Unknown"
        
        # Remove excessive whitespace
        username = ' '.join(username.split())
        
        # Apply cleaning patterns if needed
        for pattern in self.username_clean_patterns:
            username = pattern.sub('', username)
        
        return username.strip() or "Unknown"
    
    def _parse_pagination_info(self, page_info: Dict[str, Any]) -> PaginationInfo:
        """Parse pagination information"""
        return PaginationInfo(
            has_next_page=page_info.get('has_next_page', False),
            has_previous_page=page_info.get('has_previous_page', False),
            start_cursor=page_info.get('start_cursor'),
            end_cursor=page_info.get('end_cursor')
        )
    
    def _deduplicate_comments(self, comments: List[FacebookComment]) -> List[FacebookComment]:
        """Remove duplicate comments"""
        unique_comments = []
        
        for comment in comments:
            comment_key = comment.legacy_fbid or comment.comment_id
            if comment_key not in self.seen_comment_ids:
                self.seen_comment_ids.add(comment_key)
                unique_comments.append(comment)
        
        return unique_comments
    
    def _deduplicate_users(self, users: List[FacebookUser]) -> List[FacebookUser]:
        """Remove duplicate users"""
        unique_users = []
        
        for user in users:
            if user.uid not in self.seen_user_ids:
                self.seen_user_ids.add(user.uid)
                unique_users.append(user)
        
        return unique_users
    
    def extract_uids_from_text(self, text: str) -> List[str]:
        """Extract UIDs from text using regex patterns"""
        uids = []
        
        for pattern in self.uid_patterns:
            matches = pattern.findall(text)
            uids.extend(matches)
        
        # Filter and validate UIDs
        valid_uids = []
        for uid in uids:
            if uid.isdigit() and 8 <= len(uid) <= 20:
                valid_uids.append(uid)
        
        return list(set(valid_uids))  # Remove duplicates
    
    def reset_deduplication(self):
        """Reset deduplication tracking"""
        self.seen_comment_ids.clear()
        self.seen_user_ids.clear()
    
    def get_extraction_stats(self) -> Dict[str, int]:
        """Get extraction statistics"""
        return {
            'unique_comments_seen': len(self.seen_comment_ids),
            'unique_users_seen': len(self.seen_user_ids)
        }


# Factory function
def create_facebook_parser() -> FacebookDataParser:
    """Create Facebook data parser instance"""
    return FacebookDataParser()
