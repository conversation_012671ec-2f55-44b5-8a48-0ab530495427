"""
Real Facebook Test Script
Tests API interception with actual Facebook login and post scraping
Uses provided test credentials: <EMAIL> / DuyyeuVi@99
"""
import asyncio
import time
import json
from typing import Dict, Any
from loguru import logger
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from facebook_scraper_service import create_facebook_scraper_service
from facebook_comment_interceptor import create_facebook_comment_interceptor
from stealth_browser_manager import create_facebook_profile, ProxyConfig


class RealFacebookTester:
    """Test with real Facebook account and posts"""
    
    def __init__(self):
        self.test_credentials = {
            'email': '<EMAIL>',
            'password': 'Du<PERSON>yeuV<PERSON>@99'
        }
        self.scraper_service = None
        
    async def setup(self):
        """Setup test environment"""
        try:
            self.scraper_service = await create_facebook_scraper_service("real_test_data")
            logger.info("Real Facebook test environment setup completed")
            return True
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            return False
    
    async def test_manual_login_workflow(self):
        """Test manual login workflow with real Facebook account"""
        logger.info("🔐 Starting manual login workflow test")
        
        try:
            # Create profile for manual login
            profile_config = {
                'profile_id': 'real_facebook_test',
                'profile_name': 'Real Facebook Test Profile',
                'user_data_dir': './real_test_profiles/facebook_login',
                'proxy': None
            }
            
            # Create interceptor for manual login
            interceptor = await create_facebook_comment_interceptor(
                profile_config=profile_config,
                max_comments=10,
                headless=False  # Visible for manual login
            )
            
            # Navigate to Facebook login
            logger.info("🌐 Opening Facebook login page...")
            await interceptor.tab.get("https://www.facebook.com/login")
            await asyncio.sleep(3)
            
            # Instructions for manual login
            logger.info("👤 Please manually log in with the following credentials:")
            logger.info(f"   Email: {self.test_credentials['email']}")
            logger.info(f"   Password: {self.test_credentials['password']}")
            logger.info("   Complete any 2FA/security checks if required")
            logger.info("   Press ENTER in this terminal when login is complete...")
            
            # Wait for user to complete login
            input("Press ENTER after completing Facebook login...")
            
            # Verify login by checking current URL
            current_url = await interceptor.tab.evaluate("window.location.href")
            logger.info(f"Current URL after login: {current_url}")
            
            if "facebook.com" in current_url and "login" not in current_url:
                logger.info("✅ Login appears successful!")
                
                # Save cookies for future use
                cookies = await interceptor.tab.send(interceptor.tab.cdp.network.get_all_cookies())
                logger.info(f"Saved {len(cookies.cookies)} cookies for future sessions")
                
                return True
            else:
                logger.warning("⚠️ Login may not be complete or failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Manual login test failed: {e}")
            return False
        
        finally:
            if 'interceptor' in locals():
                await interceptor.cleanup()
    
    async def test_real_post_scraping(self, post_url: str = None):
        """Test scraping a real Facebook post"""
        logger.info("📝 Starting real post scraping test")
        
        if not post_url:
            # Use a public Facebook post for testing
            post_url = input("Enter Facebook post URL to test (or press ENTER for default): ").strip()
            if not post_url:
                # Default to a public post (you should replace with actual public post)
                post_url = "https://www.facebook.com/groups/591054007361950/posts/8068194536314616/"
                logger.info(f"Using default post URL: {post_url}")
        
        try:
            profile_config = {
                'profile_id': 'real_scraping_test',
                'profile_name': 'Real Scraping Test',
                'user_data_dir': './real_test_profiles/scraping_test',
                'proxy': None
            }
            
            logger.info(f"🎯 Scraping post: {post_url}")
            
            start_time = time.time()
            result = await self.scraper_service.scrape_facebook_post_uids(
                profile_config=profile_config,
                post_url=post_url,
                max_comments=100
            )
            
            scraping_time = time.time() - start_time
            
            if result.get("success", False):
                results = result["results"]
                
                logger.info("✅ Real post scraping successful!")
                logger.info(f"   📊 Comments extracted: {results.get('total_comments_extracted', 0)}")
                logger.info(f"   👥 UIDs found: {results.get('total_uids_found', 0)}")
                logger.info(f"   🆕 New UIDs: {len(results.get('new_uids', []))}")
                logger.info(f"   🔄 Duplicates: {results.get('duplicate_count', 0)}")
                logger.info(f"   ⏱️ Processing time: {scraping_time:.2f}s")
                
                # Show API interception stats
                api_stats = results.get('api_interception_stats', {})
                logger.info(f"   📡 API calls intercepted: {api_stats.get('api_calls_intercepted', 0)}")
                logger.info(f"   📜 Scroll attempts: {api_stats.get('scroll_attempts', 0)}")
                
                # Show sample UIDs
                new_uids = results.get('new_uids', [])
                if new_uids:
                    logger.info(f"   🔢 Sample UIDs: {new_uids[:5]}...")
                
                # Show sample user details
                user_details = results.get('user_details', [])
                if user_details:
                    logger.info("   👤 Sample users:")
                    for user in user_details[:3]:
                        logger.info(f"      - UID: {user.get('uid', 'N/A')}, Name: {user.get('username', 'N/A')}")
                
                return True
            else:
                logger.error(f"❌ Real post scraping failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Real post scraping test failed: {e}")
            return False
    
    async def test_performance_with_real_data(self):
        """Performance test with real Facebook data"""
        logger.info("⚡ Starting performance test with real data")
        
        try:
            profile_config = {
                'profile_id': 'performance_test_real',
                'profile_name': 'Performance Test Real',
                'user_data_dir': './real_test_profiles/performance_test',
                'proxy': None
            }
            
            # Test with higher comment limit
            post_url = input("Enter Facebook post URL for performance test: ").strip()
            if not post_url:
                logger.warning("No URL provided, skipping performance test")
                return False
            
            max_comments = int(input("Enter max comments to extract (default 500): ") or "500")
            
            logger.info(f"🎯 Performance testing with {max_comments} max comments")
            
            start_time = time.time()
            result = await self.scraper_service.scrape_facebook_post_uids(
                profile_config=profile_config,
                post_url=post_url,
                max_comments=max_comments
            )
            
            total_time = time.time() - start_time
            
            if result.get("success", False):
                results = result["results"]
                
                # Calculate performance metrics
                comments_extracted = results.get('total_comments_extracted', 0)
                uids_found = results.get('total_uids_found', 0)
                api_calls = results.get('api_interception_stats', {}).get('api_calls_intercepted', 0)
                
                comments_per_second = comments_extracted / total_time if total_time > 0 else 0
                uids_per_second = uids_found / total_time if total_time > 0 else 0
                api_efficiency = comments_extracted / api_calls if api_calls > 0 else 0
                
                logger.info("✅ Performance test completed!")
                logger.info(f"   📊 Total time: {total_time:.2f}s")
                logger.info(f"   💬 Comments/second: {comments_per_second:.2f}")
                logger.info(f"   👥 UIDs/second: {uids_per_second:.2f}")
                logger.info(f"   📡 API efficiency: {api_efficiency:.2f} comments/API call")
                
                # Performance evaluation
                if comments_per_second > 5:
                    logger.info("🚀 Excellent performance!")
                elif comments_per_second > 2:
                    logger.info("✅ Good performance")
                else:
                    logger.warning("⚠️ Performance could be improved")
                
                return True
            else:
                logger.error(f"❌ Performance test failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Performance test failed: {e}")
            return False
    
    async def test_comment_data_accuracy(self):
        """Test accuracy of extracted comment data"""
        logger.info("🎯 Testing comment data accuracy")
        
        try:
            profile_config = {
                'profile_id': 'accuracy_test',
                'profile_name': 'Accuracy Test',
                'user_data_dir': './real_test_profiles/accuracy_test',
                'proxy': None
            }
            
            # Create interceptor for detailed analysis
            interceptor = await create_facebook_comment_interceptor(
                profile_config=profile_config,
                max_comments=20,  # Small number for detailed analysis
                headless=False
            )
            
            post_url = input("Enter Facebook post URL for accuracy test: ").strip()
            if not post_url:
                logger.warning("No URL provided, skipping accuracy test")
                return False
            
            logger.info("🔍 Extracting comments for accuracy analysis...")
            
            extraction_result = await interceptor.extract_comments(post_url)
            
            logger.info("📋 Comment data analysis:")
            logger.info(f"   Total comments: {extraction_result.total_extracted}")
            
            # Analyze comment data quality
            valid_uids = 0
            valid_usernames = 0
            non_empty_comments = 0
            
            for comment in extraction_result.comments:
                if comment.uid and comment.uid.isdigit() and len(comment.uid) >= 8:
                    valid_uids += 1
                
                if comment.username and comment.username.strip() and comment.username != "Unknown":
                    valid_usernames += 1
                
                if comment.comment_text and comment.comment_text.strip():
                    non_empty_comments += 1
            
            total_comments = len(extraction_result.comments)
            
            if total_comments > 0:
                uid_accuracy = (valid_uids / total_comments) * 100
                username_accuracy = (valid_usernames / total_comments) * 100
                content_accuracy = (non_empty_comments / total_comments) * 100
                
                logger.info(f"   ✅ UID accuracy: {uid_accuracy:.1f}% ({valid_uids}/{total_comments})")
                logger.info(f"   ✅ Username accuracy: {username_accuracy:.1f}% ({valid_usernames}/{total_comments})")
                logger.info(f"   ✅ Content accuracy: {content_accuracy:.1f}% ({non_empty_comments}/{total_comments})")
                
                # Show sample data
                logger.info("   📝 Sample comments:")
                for i, comment in enumerate(extraction_result.comments[:3]):
                    logger.info(f"      {i+1}. UID: {comment.uid}, User: {comment.username}")
                    logger.info(f"         Text: {comment.comment_text[:100]}...")
                
                overall_accuracy = (uid_accuracy + username_accuracy + content_accuracy) / 3
                
                if overall_accuracy > 90:
                    logger.info("🎉 Excellent data accuracy!")
                elif overall_accuracy > 75:
                    logger.info("✅ Good data accuracy")
                else:
                    logger.warning("⚠️ Data accuracy needs improvement")
                
                return True
            else:
                logger.warning("⚠️ No comments extracted for accuracy analysis")
                return False
                
        except Exception as e:
            logger.error(f"❌ Accuracy test failed: {e}")
            return False
        
        finally:
            if 'interceptor' in locals():
                await interceptor.cleanup()
    
    async def run_interactive_tests(self):
        """Run interactive tests with user input"""
        logger.info("🚀 Starting Real Facebook Interactive Tests")
        
        if not await self.setup():
            logger.error("❌ Test setup failed")
            return
        
        tests = {
            "1": ("Manual Login Workflow", self.test_manual_login_workflow),
            "2": ("Real Post Scraping", self.test_real_post_scraping),
            "3": ("Performance Test", self.test_performance_with_real_data),
            "4": ("Data Accuracy Test", self.test_comment_data_accuracy),
            "5": ("Run All Tests", self.run_all_tests)
        }
        
        while True:
            print("\n" + "="*50)
            print("Real Facebook API Interception Tests")
            print("="*50)
            
            for key, (name, _) in tests.items():
                print(f"{key}. {name}")
            print("0. Exit")
            
            choice = input("\nSelect test to run (0-5): ").strip()
            
            if choice == "0":
                break
            elif choice in tests:
                test_name, test_func = tests[choice]
                logger.info(f"Running: {test_name}")
                
                try:
                    if choice == "5":
                        await self.run_all_tests()
                    else:
                        await test_func()
                except Exception as e:
                    logger.error(f"Test failed: {e}")
                
                input("\nPress ENTER to continue...")
            else:
                print("Invalid choice. Please try again.")
        
        # Cleanup
        if self.scraper_service:
            await self.scraper_service.cleanup()
        
        logger.info("👋 Tests completed. Goodbye!")
    
    async def run_all_tests(self):
        """Run all tests sequentially"""
        logger.info("🔄 Running all tests...")
        
        tests = [
            self.test_real_post_scraping,
            self.test_comment_data_accuracy
        ]
        
        for test_func in tests:
            try:
                logger.info(f"▶️ Running {test_func.__name__}...")
                await test_func()
                await asyncio.sleep(2)
            except Exception as e:
                logger.error(f"Test {test_func.__name__} failed: {e}")


async def main():
    """Main function"""
    tester = RealFacebookTester()
    await tester.run_interactive_tests()


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    # Run interactive tests
    asyncio.run(main())
