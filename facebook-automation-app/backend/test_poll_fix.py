#!/usr/bin/env python3
"""
Test to verify poll() method fix
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
from pathlib import Path

async def test_multiple_check_calls():
    """Test multiple Check button calls to catch poll() error"""
    print("🧪 Testing multiple Check button calls to catch poll() error...")
    
    try:
        # Create a test profile
        profile_data = {
            "name": f"Poll Fix Test {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created profile: {profile['name']} (ID: {profile_id})")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "123456789"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return False
            
            # Create mock cookies directory
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create mock cookies file
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock cookies for poll test")
            print(f"✅ Created mock cookies file")
            
            # Test multiple Check button calls
            success_count = 0
            error_count = 0
            
            for i in range(5):
                print(f"\n🚀 Test call #{i+1}...")
                
                try:
                    async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                        response_text = await resp.text()
                        
                        if resp.status == 200:
                            result = json.loads(response_text)
                            
                            if result.get('status') == 'browser_launched':
                                print(f"   ✅ Call #{i+1} SUCCESS: {result.get('message')}")
                                success_count += 1
                            elif result.get('status') == 'session_exists':
                                print(f"   ✅ Call #{i+1} SESSION EXISTS: {result.get('message')}")
                                success_count += 1
                            else:
                                print(f"   ❌ Call #{i+1} UNEXPECTED STATUS: {result.get('status')}")
                                print(f"      Message: {result.get('message')}")
                                error_count += 1
                        else:
                            print(f"   ❌ Call #{i+1} HTTP ERROR: {resp.status}")
                            print(f"      Response: {response_text}")
                            error_count += 1
                            
                            # Check if it's the poll() error
                            if "'Process' object has no attribute 'poll'" in response_text:
                                print(f"   🚨 POLL ERROR DETECTED in call #{i+1}!")
                                return False
                                
                except Exception as e:
                    print(f"   ❌ Call #{i+1} EXCEPTION: {e}")
                    error_count += 1
                
                # Wait between calls
                await asyncio.sleep(1)
            
            print(f"\n📊 Test Results:")
            print(f"   Success calls: {success_count}")
            print(f"   Error calls: {error_count}")
            print(f"   Total calls: {success_count + error_count}")
            
            if error_count == 0:
                print(f"✅ All calls successful - poll() error fixed!")
                result = True
            else:
                print(f"❌ Some calls failed - need investigation")
                result = False
            
            # Clean up
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print(f"✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            import shutil
            if profile_dir.exists():
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
            
            return result
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_management():
    """Test session management to ensure no poll() errors"""
    print("\n🧪 Testing session management...")
    
    try:
        # Create a test profile
        profile_data = {
            "name": f"Session Test {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created profile: {profile['name']}")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "987654321"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return False
            
            # Create mock cookies
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock cookies for session test")
            print(f"✅ Created mock cookies file")
            
            # Test session lifecycle
            print(f"\n🚀 Testing session lifecycle...")
            
            # 1. First call - should create session
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get('status') == 'browser_launched':
                        print(f"✅ Session created successfully")
                        session_id = result.get('session_id')
                    else:
                        print(f"❌ Unexpected status: {result.get('status')}")
                        return False
                else:
                    print(f"❌ First call failed: {resp.status}")
                    return False
            
            # 2. Second call - should detect existing session
            await asyncio.sleep(1)
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get('status') == 'session_exists':
                        print(f"✅ Existing session detected correctly")
                    elif result.get('status') == 'browser_launched':
                        print(f"✅ New session created (previous may have ended)")
                    else:
                        print(f"❌ Unexpected status: {result.get('status')}")
                        return False
                else:
                    print(f"❌ Second call failed: {resp.status}")
                    return False
            
            print(f"✅ Session management test passed!")
            
            # Clean up
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print(f"✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            import shutil
            if profile_dir.exists():
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
            
            return True
                    
    except Exception as e:
        print(f"❌ Session test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Poll() Method Fix Test")
    print("This test verifies that the poll() method error is fixed")
    print("=" * 60)
    
    # Test 1: Multiple calls
    success1 = asyncio.run(test_multiple_check_calls())
    
    # Test 2: Session management
    success2 = asyncio.run(test_session_management())
    
    print("\n" + "=" * 60)
    print("📊 Final Results:")
    print(f"   Multiple calls test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Session management test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎊 ALL TESTS PASSED!")
        print("   ✅ poll() method error fixed")
        print("   ✅ Multiple Check button clicks work")
        print("   ✅ Session management working")
        print("   ✅ No more intermittent errors")
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("   ❌ poll() method error may still exist")
        print("   🔧 Further investigation needed")
    
    print(f"\nTest completed.")
