#!/usr/bin/env python3
"""
Test API endpoint directly
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8000"

async def test_profile_creation_and_login():
    """Test profile creation and Facebook login"""
    print("=== Testing Profile Creation and Facebook Login ===")
    
    # Profile data
    profile_data = {
        "name": f"Test Profile {int(time.time())}",
        "description": "Test profile for debugging",
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "screen_resolution": "1920x1080",
        "timezone": "UTC",
        "language": "en-US",
        "facebook_email": "<EMAIL>",
        "facebook_password": "DuyyeuVi@99",
        "facebook_username": "tranduy8683",
        "proxy_type": "no_proxy"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # Step 1: Create profile
            print("1. Creating profile...")
            async with session.post(f"{BASE_URL}/api/profiles/", json=profile_data) as response:
                if response.status == 201:
                    profile = await response.json()
                    profile_id = profile['id']
                    print(f"✓ Profile created: {profile['name']} (ID: {profile_id})")
                else:
                    error = await response.text()
                    print(f"✗ Profile creation failed: {response.status} - {error}")
                    return None
            
            # Step 2: Test Facebook login
            print("2. Testing Facebook login...")
            async with session.post(f"{BASE_URL}/api/profiles/{profile_id}/facebook-login") as response:
                result = await response.json()
                
                print(f"Login response status: {response.status}")
                print(f"Login result: {json.dumps(result, indent=2)}")
                
                if response.status == 200:
                    if result.get("status") == "browser_launched":
                        print("✓ Facebook login initiated successfully!")
                        print("Instructions:")
                        for instruction in result.get("instructions", []):
                            print(f"  - {instruction}")
                        return profile_id
                    else:
                        print(f"⚠ Login initiated with status: {result.get('status')}")
                        return profile_id
                else:
                    print(f"✗ Facebook login failed: {response.status}")
                    print(f"Error details: {result}")
                    return None
                    
        except Exception as e:
            print(f"✗ Test error: {e}")
            import traceback
            traceback.print_exc()
            return None

async def test_server_connectivity():
    """Test if server is running"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✓ Server is running and accessible")
                    return True
                else:
                    print(f"✗ Server responded with status: {response.status}")
                    return False
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        return False

async def cleanup_profile(profile_id):
    """Clean up test profile"""
    if not profile_id:
        return
        
    print(f"3. Cleaning up profile {profile_id}...")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.delete(f"{BASE_URL}/api/profiles/{profile_id}") as response:
                if response.status == 200:
                    print("✓ Profile cleaned up successfully")
                else:
                    print(f"⚠ Profile cleanup failed: {response.status}")
        except Exception as e:
            print(f"⚠ Cleanup error: {e}")

async def main():
    """Main test function"""
    print("=== API Endpoint Test ===")
    print("Testing Facebook login API endpoint directly")
    print("Make sure the server is running on http://localhost:8000\n")
    
    # Test server connectivity
    if not await test_server_connectivity():
        print("\nPlease start the server with: python3 main.py")
        return
    
    # Test profile creation and login
    profile_id = await test_profile_creation_and_login()
    
    if profile_id:
        print(f"\n✓ Test completed successfully!")
        print(f"Profile ID: {profile_id}")
        print("\nYou should see a browser window open for Facebook login.")
        print("Complete the login manually and then click 'Complete Login' in the app.")
        
        # Wait for user to complete login
        input("\nPress Enter after completing Facebook login to cleanup...")
        
        # Cleanup
        await cleanup_profile(profile_id)
    else:
        print("\n✗ Test failed. Check the error messages above.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
