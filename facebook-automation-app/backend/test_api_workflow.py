#!/usr/bin/env python3
"""
Test script to verify the complete API workflow for Facebook automation
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8000"

async def test_profile_creation():
    """Test profile creation"""
    print("\n=== Testing Profile Creation ===")
    
    profile_data = {
        "name": "Test Profile API",
        "description": "Test profile for API workflow",
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "screen_resolution": "1920x1080",
        "timezone": "UTC",
        "language": "en-US",
        "facebook_email": "<EMAIL>",
        "facebook_password": "DuyyeuVi@99",
        "facebook_username": "tranduy8683",
        "proxy_type": "no_proxy"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{BASE_URL}/api/profiles/", json=profile_data) as response:
                if response.status == 201:
                    result = await response.json()
                    print(f"✓ Profile created successfully: {result['name']} (ID: {result['id']})")
                    return result['id']
                else:
                    error = await response.text()
                    print(f"✗ Profile creation failed: {response.status} - {error}")
                    return None
        except Exception as e:
            print(f"✗ Profile creation error: {e}")
            return None

async def test_facebook_login(profile_id):
    """Test Facebook login"""
    print(f"\n=== Testing Facebook Login for Profile {profile_id} ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{BASE_URL}/api/profiles/{profile_id}/facebook-login") as response:
                result = await response.json()
                print(f"Login result: {result}")
                
                if result.get("status") in ["browser_launched", "session_exists", "fallback_initiated"]:
                    print("✓ Facebook login initiated successfully")
                    print("Instructions:")
                    for instruction in result.get("instructions", []):
                        print(f"  - {instruction}")
                    return True
                else:
                    print(f"✗ Facebook login failed: {result}")
                    return False
        except Exception as e:
            print(f"✗ Facebook login error: {e}")
            return False

async def test_facebook_status(profile_id):
    """Test Facebook status check"""
    print(f"\n=== Testing Facebook Status for Profile {profile_id} ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/profiles/{profile_id}/facebook-status") as response:
                result = await response.json()
                print(f"Facebook status: {result}")
                
                if result.get("is_logged_in"):
                    print("✓ Profile is logged into Facebook")
                    return True
                else:
                    print("ℹ Profile is not logged into Facebook yet")
                    return False
        except Exception as e:
            print(f"✗ Facebook status check error: {e}")
            return False

async def test_scraping_session_creation(profile_id):
    """Test scraping session creation"""
    print(f"\n=== Testing Scraping Session Creation for Profile {profile_id} ===")
    
    scraping_data = {
        "name": "Test Scraping Session",
        "post_url": "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
        "profile_id": profile_id,
        "scraping_types": ["comments"],
        "include_comments": True,
        "include_likes": False,
        "include_shares": False,
        "include_reactions": False
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{BASE_URL}/api/scraping/sessions", json=scraping_data) as response:
                if response.status == 201:
                    result = await response.json()
                    print(f"✓ Scraping session created: {result['name']} (ID: {result['id']})")
                    return result['id']
                else:
                    error = await response.text()
                    print(f"✗ Scraping session creation failed: {response.status} - {error}")
                    return None
        except Exception as e:
            print(f"✗ Scraping session creation error: {e}")
            return None

async def test_scraping_start(session_id):
    """Test starting scraping session"""
    print(f"\n=== Testing Scraping Start for Session {session_id} ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{BASE_URL}/api/scraping/sessions/{session_id}/start") as response:
                result = await response.json()
                print(f"Scraping start result: {result}")
                
                if result.get("message") and "started" in result.get("message", "").lower():
                    print("✓ Scraping session started successfully")
                    return True
                else:
                    print(f"✗ Scraping start failed: {result}")
                    return False
        except Exception as e:
            print(f"✗ Scraping start error: {e}")
            return False

async def test_scraping_progress(session_id):
    """Test scraping progress monitoring"""
    print(f"\n=== Testing Scraping Progress for Session {session_id} ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/progress") as response:
                result = await response.json()
                print(f"Scraping progress: {result}")
                
                status = result.get("status", "unknown")
                progress = result.get("progress_percentage", 0)
                step = result.get("current_step", "Unknown")
                
                print(f"Status: {status}, Progress: {progress}%, Step: {step}")
                return True
        except Exception as e:
            print(f"✗ Scraping progress check error: {e}")
            return False

async def test_profile_cleanup(profile_id):
    """Test profile cleanup"""
    print(f"\n=== Testing Profile Cleanup for Profile {profile_id} ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.delete(f"{BASE_URL}/api/profiles/{profile_id}") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✓ Profile deleted successfully: {result}")
                    return True
                else:
                    error = await response.text()
                    print(f"✗ Profile deletion failed: {response.status} - {error}")
                    return False
        except Exception as e:
            print(f"✗ Profile cleanup error: {e}")
            return False

async def main():
    """Main test function"""
    print("=== Facebook Automation API Workflow Test ===")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    
    # Test server connectivity
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✓ Server is running and accessible")
                else:
                    print("✗ Server is not accessible")
                    return
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        print("Please start the server with: python3 main.py")
        return
    
    results = {}
    profile_id = None
    session_id = None
    
    try:
        # Test 1: Create profile
        profile_id = await test_profile_creation()
        results["profile_creation"] = profile_id is not None
        
        if profile_id:
            # Test 2: Facebook login
            results["facebook_login"] = await test_facebook_login(profile_id)
            
            # Test 3: Facebook status
            results["facebook_status"] = await test_facebook_status(profile_id)
            
            # Test 4: Create scraping session
            session_id = await test_scraping_session_creation(profile_id)
            results["scraping_session_creation"] = session_id is not None
            
            if session_id:
                # Test 5: Start scraping (this will likely fail without proper login)
                results["scraping_start"] = await test_scraping_start(session_id)
                
                # Test 6: Check progress
                results["scraping_progress"] = await test_scraping_progress(session_id)
    
    finally:
        # Cleanup
        if profile_id:
            results["profile_cleanup"] = await test_profile_cleanup(profile_id)
    
    # Summary
    print("\n" + "="*60)
    print("API WORKFLOW TEST RESULTS:")
    print("="*60)
    
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name:25} {status}")
    
    print("="*60)
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    print(f"Tests passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("✓ ALL API TESTS PASSED!")
    else:
        print("ℹ Some tests failed - this is expected if Facebook login is not completed manually")
    
    print("\nAPI workflow test completed.")

if __name__ == "__main__":
    asyncio.run(main())
