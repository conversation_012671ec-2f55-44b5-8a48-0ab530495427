#!/usr/bin/env python3
"""
Debug Chrome launch process
"""

import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.zendriver_service import ZendriverService
from app.services.browser_service import BrowserService
from app.models.profile import Profile

async def debug_chrome_launch():
    """Debug Chrome launch step by step"""
    print("🔍 Debugging Chrome launch process...")
    
    try:
        # Create a mock profile for testing
        profile = Profile(
            id="debug-chrome-123",
            name="Debug Chrome Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US",
            proxy_type="no_proxy",
            facebook_email="<EMAIL>",
            facebook_user_id="123456789"
        )
        
        print(f"✓ Created test profile: {profile.name}")
        
        # Create mock cookies
        browser_service = BrowserService()
        browser_config = browser_service.create_browser_config(profile)
        profile_path = browser_config["profile_path"]
        cookies_file = os.path.join(profile_path, "Default", "Cookies")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(cookies_file), exist_ok=True)
        
        # Create mock cookies file
        with open(cookies_file, 'w') as f:
            f.write("mock cookies data")
        
        print(f"✓ Created mock cookies at: {cookies_file}")
        print(f"✓ Profile path: {profile_path}")
        
        # Test Chrome executable path
        chrome_path = browser_service._get_chrome_executable_path()
        print(f"✓ Chrome path: {chrome_path}")
        print(f"✓ Chrome exists: {os.path.exists(chrome_path) if chrome_path else False}")
        
        # Test zendriver service
        zendriver_service = ZendriverService()
        
        # Try direct Chrome launch
        print("\n🚀 Testing direct Chrome launch...")
        try:
            result = await zendriver_service._launch_chrome_directly(profile)
            print(f"✅ Direct Chrome launch result: {result}")
            return True
        except Exception as e:
            print(f"❌ Direct Chrome launch failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Try to get more details about the failure
            print("\n🔍 Debugging Chrome launch failure...")
            
            # Test Chrome command manually
            chrome_args = [
                chrome_path,
                f"--user-data-dir={profile_path}",
                "--no-first-run",
                "--disable-dev-shm-usage",
                "--remote-allow-origins=*",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--new-window",
                "--start-maximized",
                "--disable-background-mode",
                "https://www.facebook.com/"
            ]
            
            print(f"Chrome command: {' '.join(chrome_args)}")
            
            # Try launching manually
            import subprocess
            try:
                result = subprocess.run(chrome_args, 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=10)
                
                print(f"Manual launch result:")
                print(f"  Return code: {result.returncode}")
                print(f"  STDOUT: {result.stdout}")
                print(f"  STDERR: {result.stderr}")
                
            except subprocess.TimeoutExpired:
                print("✅ Chrome launched successfully (timeout expected)")
                return True
            except Exception as manual_error:
                print(f"❌ Manual launch failed: {manual_error}")
                return False
            
            return False
            
    except Exception as e:
        print(f"❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_chrome_launch())
    print(f"\n{'✅ Debug PASSED' if success else '❌ Debug FAILED'}")
