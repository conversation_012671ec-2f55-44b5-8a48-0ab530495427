#!/usr/bin/env python3
"""
Test script to debug zendriver browser launch issues
"""
import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add zendriver to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "zendriver_local"))

try:
    import zendriver as zd
    print("✓ Zendriver imported successfully")
except ImportError as e:
    print(f"✗ Failed to import zendriver: {e}")
    sys.exit(1)

async def test_basic_launch():
    """Test basic zendriver browser launch"""
    try:
        print("Testing basic zendriver browser launch...")
        
        # Create temporary profile directory
        temp_dir = tempfile.mkdtemp(prefix="zendriver_test_")
        print(f"Using temp profile directory: {temp_dir}")
        
        # Basic configuration
        config = {
            "user_data_dir": temp_dir,
            "headless": False,
            "sandbox": False,
            "browser_executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "browser_connection_timeout": 10.0,
            "browser_connection_max_tries": 3,
            "browser_args": [
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--no-first-run",
                "--disable-dev-shm-usage",
                "--remote-allow-origins=*"
            ]
        }
        
        print("Starting browser with config:")
        for key, value in config.items():
            if key != "browser_args":
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {len(value)} arguments")
        
        # Start browser
        browser = await zd.start(**config)
        print("✓ Browser started successfully")
        
        # Navigate to a simple page
        page = await browser.get("https://www.google.com")
        print("✓ Navigated to Google")
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Get page title
        title = await page.evaluate("document.title")
        print(f"✓ Page title: {title}")
        
        # Close browser
        await browser.stop()
        print("✓ Browser closed successfully")
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("✓ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during browser launch test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_antidetect_launch():
    """Test antidetect browser launch with profile configuration"""
    try:
        print("\nTesting antidetect browser launch...")
        
        # Create profile directory
        profile_dir = Path("browser_sessions/test_profile")
        profile_dir.mkdir(parents=True, exist_ok=True)
        
        # Antidetect configuration
        config = {
            "user_data_dir": str(profile_dir),
            "headless": False,
            "sandbox": False,
            "browser_executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "browser_connection_timeout": 15.0,
            "browser_connection_max_tries": 5,
            "browser_args": [
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-except",
                "--disable-plugins-discovery",
                "--no-first-run",
                "--no-service-autorun",
                "--password-store=basic",
                "--disable-dev-shm-usage",
                "--remote-allow-origins=*",
                "--disable-webrtc",
                "--disable-background-networking",
                "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        }
        
        print("Starting antidetect browser...")
        browser = await zd.start(**config)
        print("✓ Antidetect browser started successfully")
        
        # Navigate to Facebook login
        page = await browser.get("https://www.facebook.com/login")
        print("✓ Navigated to Facebook login page")
        
        # Set page title
        await page.evaluate("""
            document.title = 'Facebook Login - Test Profile - Complete login manually';
        """)
        print("✓ Page title updated")
        
        # Wait for manual interaction
        print("Browser is ready for manual Facebook login testing...")
        print("Press Ctrl+C to stop the test")
        
        try:
            await asyncio.sleep(60)  # Wait 1 minute for manual testing
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
        
        # Close browser
        await browser.stop()
        print("✓ Antidetect browser closed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during antidetect browser test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=== Zendriver Browser Launch Test ===\n")
    
    # Test 1: Basic launch
    basic_success = await test_basic_launch()
    
    if basic_success:
        print("\n" + "="*50)
        # Test 2: Antidetect launch
        antidetect_success = await test_antidetect_launch()
        
        if antidetect_success:
            print("\n✓ All tests passed! Zendriver is working correctly.")
        else:
            print("\n✗ Antidetect test failed, but basic launch works.")
    else:
        print("\n✗ Basic launch test failed. Check zendriver installation.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
