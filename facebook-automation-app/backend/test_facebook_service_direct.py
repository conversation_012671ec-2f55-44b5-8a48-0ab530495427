#!/usr/bin/env python3
"""
Direct test of FacebookService
"""
import asyncio
import sys
import os

# Add project paths
sys.path.insert(0, os.path.dirname(__file__))

async def test_facebook_service():
    """Test FacebookService directly"""
    try:
        print("=== Testing FacebookService Direct ===")
        
        # Import services
        from app.services.facebook_service import FacebookService
        from app.services.browser_service import BrowserService
        from app.models.profile import Profile
        
        print("✓ Services imported successfully")
        
        # Create mock profile
        profile = Profile(
            id="test-direct-123",
            name="Test Direct Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US",
            facebook_email="<EMAIL>",
            facebook_password="testpass"
        )
        
        print(f"✓ Created test profile: {profile.name}")
        
        # Initialize services
        browser_service = BrowserService()
        facebook_service = FacebookService()
        
        print("✓ Services initialized")
        
        # Test browser config generation
        zendriver_config = browser_service.get_zendriver_config(profile)
        print(f"✓ Zendriver config generated: {len(zendriver_config)} settings")
        
        # Test config validation
        is_valid, message = browser_service.validate_zendriver_config(zendriver_config)
        print(f"Config validation: {is_valid} - {message}")
        
        if not is_valid:
            print(f"✗ Configuration invalid: {message}")
            return False
        
        # Test Facebook login initiation
        print("Testing Facebook login initiation...")
        result = await facebook_service.initiate_login(profile)
        
        print(f"Login result: {result}")
        
        if result.get("status") == "login_initiated":
            print("✓ Facebook login initiated successfully")
            return True
        elif result.get("status") == "browser_launched":
            print("✓ Browser launched successfully")
            return True
        else:
            print(f"✗ Facebook login failed: {result}")
            return False
            
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_zendriver_config():
    """Test zendriver configuration generation"""
    try:
        print("\n=== Testing Zendriver Configuration ===")
        
        from app.services.browser_service import BrowserService
        from app.models.profile import Profile
        
        # Create test profile
        profile = Profile(
            id="test-config-456",
            name="Test Config Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US"
        )
        
        browser_service = BrowserService()
        
        # Generate config
        config = browser_service.get_zendriver_config(profile)
        
        print("Generated zendriver config:")
        for key, value in config.items():
            if key == "browser_args":
                print(f"  {key}: {len(value)} arguments")
            else:
                print(f"  {key}: {value}")
        
        # Validate config
        is_valid, message = browser_service.validate_zendriver_config(config)
        print(f"\nValidation result: {is_valid} - {message}")
        
        # Test Chrome path
        chrome_path = browser_service._get_chrome_executable_path()
        print(f"Chrome path: {chrome_path}")
        chrome_exists = os.path.exists(chrome_path)
        print(f"Chrome exists: {chrome_exists}")
        
        return is_valid and chrome_exists
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=== FacebookService Direct Test ===\n")
    
    # Test 1: Configuration
    config_success = await test_zendriver_config()
    
    # Test 2: FacebookService
    service_success = await test_facebook_service()
    
    # Summary
    print("\n" + "="*50)
    print("TEST RESULTS:")
    print("="*50)
    print(f"Configuration Test: {'✓ PASS' if config_success else '✗ FAIL'}")
    print(f"FacebookService Test: {'✓ PASS' if service_success else '✗ FAIL'}")
    print("="*50)
    
    if config_success and service_success:
        print("✓ ALL TESTS PASSED!")
        print("\nFacebookService is ready for use.")
    else:
        print("✗ SOME TESTS FAILED")
        print("\nCheck the errors above for details.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
