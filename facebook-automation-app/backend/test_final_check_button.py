#!/usr/bin/env python3
"""
Final test for Check button functionality
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
from pathlib import Path

async def test_check_button_final():
    """Final comprehensive test for Check button"""
    print("🧪 Final Check Button Test - Comprehensive Testing...")
    
    try:
        # Create a profile similar to user's real profile
        profile_data = {
            "name": f"Final Test DuyTran {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created profile: {profile['name']}")
                    print(f"   Profile ID: {profile_id}")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info (like user's real profile)
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "123456789"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return False
            
            # Create realistic Facebook cookies
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            cookies_file = default_dir / "Cookies"
            cookies_content = """
            # Realistic Facebook cookies for final test
            c_user=123456789
            xs=final_test_session_token
            fr=final_test_facebook_token
            datr=final_test_browser_fingerprint
            sb=final_test_session_browser_id
            """
            cookies_file.write_text(cookies_content)
            print(f"✅ Created realistic Facebook cookies")
            
            # Test scenarios
            test_results = []
            
            # Scenario 1: First Check button click
            print(f"\n🚀 Scenario 1: First Check button click...")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get('status') == 'browser_launched':
                        print(f"   ✅ First click SUCCESS")
                        print(f"      Message: {result.get('message')}")
                        test_results.append(True)
                    else:
                        print(f"   ❌ First click FAILED: {result.get('status')}")
                        test_results.append(False)
                else:
                    print(f"   ❌ First click HTTP ERROR: {resp.status}")
                    test_results.append(False)
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Scenario 2: Second Check button click (should work without poll error)
            print(f"\n🚀 Scenario 2: Second Check button click...")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                response_text = await resp.text()
                if resp.status == 200:
                    result = json.loads(response_text)
                    if result.get('status') == 'browser_launched':
                        print(f"   ✅ Second click SUCCESS")
                        print(f"      Message: {result.get('message')}")
                        test_results.append(True)
                    else:
                        print(f"   ❌ Second click UNEXPECTED: {result.get('status')}")
                        test_results.append(False)
                else:
                    print(f"   ❌ Second click HTTP ERROR: {resp.status}")
                    print(f"      Response: {response_text}")
                    # Check for poll error specifically
                    if "'Process' object has no attribute 'poll'" in response_text:
                        print(f"   🚨 POLL ERROR DETECTED!")
                    test_results.append(False)
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Scenario 3: Third Check button click (stress test)
            print(f"\n🚀 Scenario 3: Third Check button click...")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                response_text = await resp.text()
                if resp.status == 200:
                    result = json.loads(response_text)
                    if result.get('status') == 'browser_launched':
                        print(f"   ✅ Third click SUCCESS")
                        print(f"      Message: {result.get('message')}")
                        test_results.append(True)
                    else:
                        print(f"   ❌ Third click UNEXPECTED: {result.get('status')}")
                        test_results.append(False)
                else:
                    print(f"   ❌ Third click HTTP ERROR: {resp.status}")
                    print(f"      Response: {response_text}")
                    # Check for poll error specifically
                    if "'Process' object has no attribute 'poll'" in response_text:
                        print(f"   🚨 POLL ERROR DETECTED!")
                    test_results.append(False)
            
            # Results summary
            success_count = sum(test_results)
            total_count = len(test_results)
            
            print(f"\n📊 Test Results Summary:")
            print(f"   Successful scenarios: {success_count}/{total_count}")
            print(f"   Success rate: {(success_count/total_count)*100:.1f}%")
            
            if success_count == total_count:
                print(f"\n✅ ALL SCENARIOS PASSED!")
                print(f"   ✅ No poll() errors detected")
                print(f"   ✅ Multiple Check button clicks work")
                print(f"   ✅ Browser launches consistently")
                print(f"   ✅ Session management working")
                
                print(f"\n🎯 Profile ready for frontend testing:")
                print(f"   Profile Name: {profile['name']}")
                print(f"   Profile ID: {profile_id}")
                print(f"   Facebook Email: <EMAIL>")
                print(f"   Status: Ready for production ✅")
                
                print(f"\n📋 Frontend Testing:")
                print(f"   1. Go to http://localhost:3001")
                print(f"   2. Navigate to Profiles page")
                print(f"   3. Find profile: {profile['name']}")
                print(f"   4. Click 'Check' button multiple times")
                print(f"   5. Verify browser opens each time")
                
                result = True
            else:
                print(f"\n❌ SOME SCENARIOS FAILED!")
                print(f"   🔧 Check button needs investigation")
                result = False
            
            # Clean up
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print(f"\n✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            import shutil
            if profile_dir.exists():
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
            
            return result
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Final Check Button Test")
    print("This test verifies Check button works reliably without poll() errors")
    print("=" * 70)
    
    success = asyncio.run(test_check_button_final())
    
    print("\n" + "=" * 70)
    if success:
        print("🎊 FINAL TEST PASSED!")
        print("   ✅ Check button functionality working perfectly")
        print("   ✅ No poll() method errors")
        print("   ✅ Multiple clicks supported")
        print("   ✅ Browser launches consistently")
        print("   ✅ Ready for production use")
        
        print(f"\n🌐 Frontend: http://localhost:3001")
        print(f"🔧 Backend: http://localhost:8000")
        print(f"\n🎯 Check button is ready for users!")
        
    else:
        print("💥 FINAL TEST FAILED!")
        print("   ❌ Check button has issues")
        print("   🔧 Further investigation needed")
    
    print(f"\nFinal test completed.")
