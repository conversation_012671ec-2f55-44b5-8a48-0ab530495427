#!/usr/bin/env python3
"""
Test script for checking profile functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.zendriver_service import ZendriverService
from app.models.profile import Profile

async def test_check_profile():
    """Test the check profile functionality"""
    print("🧪 Testing check profile functionality...")
    
    try:
        # Create a mock profile for testing
        profile = Profile(
            id="test-profile-123",
            name="Test Profile",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            screen_resolution="1920x1080",
            timezone="UTC",
            language="en-US",
            proxy_type="no_proxy",
            facebook_email="<EMAIL>",
            facebook_user_id="123456789"
        )
        
        # Initialize zendriver service
        zendriver_service = ZendriverService()
        
        # Test the check profile with cookies function
        result = await zendriver_service.launch_browser_with_cookies(profile)
        
        print(f"Result: {result}")
        
        if result["status"] == "browser_launched":
            print("✅ Check profile test passed!")
            return True
        else:
            print(f"❌ Check profile test failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_check_profile())
    sys.exit(0 if success else 1)
