#!/usr/bin/env python3
"""
Facebook Automation App - Backend Server
FastAPI main application optimized for desktop app performance.
"""

import os
import sys
import asyncio
from pathlib import Path
from contextlib import asynccontextmanager

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger
from dotenv import load_dotenv

# Import database components
from app.core import settings, init_database, close_database

# Import API routes - now using unified async versions
from api.profiles import router as profiles_router  # unified async version
from api.scraping import router as scraping_router  # unified async version

# Import other API routes (keeping existing ones for compatibility)
try:
    from api import campaigns_router, messaging_router, analytics_router
except ImportError:
    # Mock routers if not available
    from fastapi import APIRouter
    campaigns_router = APIRouter(prefix="/api/campaigns", tags=["campaigns"])
    messaging_router = APIRouter(prefix="/api/messaging", tags=["messaging"])
    analytics_router = APIRouter(prefix="/api/analytics", tags=["analytics"])

# Import middleware with fallback
try:
    from middleware.error_handler import ErrorHandlerMiddleware
    from middleware.performance import PerformanceMiddleware
    from middleware.rate_limiter import RateLimiterMiddleware
except ImportError:
    # Mock middleware classes if not available
    class ErrorHandlerMiddleware:
        def __init__(self, app): self.app = app
    class PerformanceMiddleware:
        def __init__(self, app): self.app = app
    class RateLimiterMiddleware:
        def __init__(self, app): self.app = app

# Load environment variables
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting Facebook Automation App Backend...")

    # Initialize database
    await init_database()
    logger.info("Database initialized")

    # Setup logging
    if hasattr(settings, 'log_file') and settings.log_file:
        logger.add(settings.log_file, rotation="10 MB", retention="7 days")

    logger.info("Server starting on {}:{}", getattr(settings, 'api_host', '127.0.0.1'), getattr(settings, 'api_port', 8000))

    yield

    # Shutdown
    logger.info("Shutting down...")
    await close_database()
    logger.info("Database connections closed")


# Create FastAPI app with optimizations
app = FastAPI(
    title=getattr(settings, 'app_name', "Facebook Automation API"),
    version=getattr(settings, 'version', "1.0.0"),
    description="Backend API for Facebook automation desktop application",
    lifespan=lifespan,
    # Optimize for desktop app
    docs_url="/docs" if getattr(settings, 'debug', True) else None,
    redoc_url="/redoc" if getattr(settings, 'debug', True) else None,
)

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware with error handling
try:
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(PerformanceMiddleware)
    app.add_middleware(RateLimiterMiddleware, requests_per_minute=100, requests_per_hour=2000)
except Exception as e:
    logger.warning("Could not add custom middleware: {}", str(e))

# Configure logging
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/app.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)

# Include API routers
app.include_router(profiles_router)
app.include_router(scraping_router)
app.include_router(campaigns_router)
app.include_router(messaging_router)
app.include_router(analytics_router)

# Performance monitoring endpoints
@app.get("/api/system/performance")
async def get_performance_stats():
    """Get system performance statistics"""
    try:
        # Get performance middleware instance
        for middleware in app.user_middleware:
            if hasattr(middleware, 'cls') and hasattr(middleware.cls, 'get_performance_stats'):
                return middleware.cls.get_performance_stats()
        return {"message": "Performance middleware not available", "status": "ok"}
    except Exception as e:
        logger.error(f"Error getting performance stats: {e}")
        return {"error": "Could not retrieve performance stats"}

@app.get("/api/system/health")
async def get_system_health():
    """Get system health status"""
    try:
        from middleware.performance import ResourceMonitor
        monitor = ResourceMonitor()
        return monitor.check_resources()
    except ImportError:
        # Basic health check if ResourceMonitor not available
        import psutil
        return {
            "status": "healthy",
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
    except Exception as e:
        logger.error(f"Error checking system health: {e}")
        return {"status": "unknown", "error": str(e)}

# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint - health check"""
    return {
        "message": "Facebook Automation API is running",
        "status": "healthy",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "facebook-automation-api",
        "version": "1.0.0"
    }

# Old API endpoints removed - using new API routers instead

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    # Check if this is an HTTPException with custom detail
    if hasattr(exc, 'detail') and exc.detail != "Not Found":
        return JSONResponse(
            status_code=404,
            content={"message": exc.detail, "status": "error"}
        )

    # Default 404 for unknown endpoints
    return JSONResponse(
        status_code=404,
        content={"message": "Endpoint not found", "status": "error"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "status": "error"}
    )

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "127.0.0.1")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    
    logger.info("Starting server on {}:{}", host, port)
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if debug else "warning"
    )
