#!/usr/bin/env python3
"""
Test script for checking profile API endpoint
"""

import asyncio
import aiohttp
import json
import time

async def test_check_profile_api():
    """Test the check profile API endpoint"""
    print("🧪 Testing check profile API endpoint...")
    
    try:
        # First, create a test profile
        profile_data = {
            "name": f"Test Profile for Check {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created test profile: {profile['name']} (ID: {profile_id})")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info (simulate logged in state)
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "123456789"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
            
            # Test the check profile endpoint
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ Check profile API response: {result}")
                    
                    if result.get('status') in ['browser_launched', 'no_cookies']:
                        print("✅ Check profile test passed!")
                        print(f"   Status: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        return True
                    else:
                        print(f"❌ Unexpected status: {result.get('status')}")
                        return False
                else:
                    error_text = await resp.text()
                    print(f"❌ API call failed: {resp.status} - {error_text}")
                    return False
            
            # Clean up - delete test profile
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print("✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_check_profile_api())
    print(f"\n{'✅ Test PASSED' if success else '❌ Test FAILED'}")
