#!/usr/bin/env python3
"""
Setup a profile for frontend testing
"""

import asyncio
import aiohttp
import json
import time
import os
import tempfile
from pathlib import Path

async def setup_frontend_test_profile():
    """Create a profile ready for frontend Check button testing"""
    print("🧪 Setting up profile for frontend Check button testing...")
    
    try:
        # Create a profile for frontend testing
        profile_data = {
            "name": f"Frontend Test Profile {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created profile: {profile['name']}")
                    print(f"   Profile ID: {profile_id}")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return None
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "987654321"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
                    return None
            
            # Create mock cookies directory (simulating saved Facebook cookies)
            profiles_base_dir = os.path.join(tempfile.gettempdir(), "browser_profiles")
            profile_dir = Path(profiles_base_dir) / f"profile_{profile_id}"
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create realistic Facebook cookies file
            cookies_file = default_dir / "Cookies"
            cookies_content = """
            # Facebook cookies for frontend test profile
            # These simulate cookies saved after successful Facebook login
            c_user=987654321
            xs=frontend_session_token
            fr=facebook_frontend_token
            datr=frontend_browser_fingerprint
            sb=frontend_session_browser_id
            """
            cookies_file.write_text(cookies_content)
            print(f"✅ Created Facebook cookies file")
            
            return {
                "profile_id": profile_id,
                "profile_name": profile['name'],
                "facebook_email": "<EMAIL>",
                "cookies_path": str(cookies_file)
            }
                    
    except Exception as e:
        print(f"❌ Profile setup failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_api_before_frontend():
    """Test API to make sure it works before frontend testing"""
    print("\n🧪 Testing API before frontend testing...")
    
    profile_info = await setup_frontend_test_profile()
    if not profile_info:
        return False
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test the Check button API
            print(f"🚀 Testing Check API for profile: {profile_info['profile_name']}")
            async with session.post(f'http://localhost:8000/api/profiles/{profile_info["profile_id"]}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    
                    if result.get('status') == 'browser_launched':
                        print(f"✅ API test successful!")
                        print(f"   Status: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        
                        print(f"\n🎯 Profile ready for frontend testing:")
                        print(f"   Profile Name: {profile_info['profile_name']}")
                        print(f"   Profile ID: {profile_info['profile_id']}")
                        print(f"   Facebook Email: {profile_info['facebook_email']}")
                        print(f"   API Status: Working ✅")
                        
                        print(f"\n📋 Frontend Testing Instructions:")
                        print(f"   1. Open browser and go to: http://localhost:3001")
                        print(f"   2. Navigate to Profiles page")
                        print(f"   3. Find profile: {profile_info['profile_name']}")
                        print(f"   4. Click the 'Check' button")
                        print(f"   5. Verify that:")
                        print(f"      - Success toast message appears")
                        print(f"      - Chrome browser window opens")
                        print(f"      - Browser navigates to Facebook")
                        print(f"      - Facebook page loads with cookies")
                        
                        print(f"\n⚠️  Note: Profile will remain in system for testing")
                        print(f"   Delete it manually from frontend when done testing")
                        
                        return True
                    else:
                        print(f"❌ API test failed: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        return False
                else:
                    error_text = await resp.text()
                    print(f"❌ API call failed: {resp.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Frontend Check Button Test Setup")
    print("=" * 60)
    
    success = asyncio.run(test_api_before_frontend())
    
    print("\n" + "=" * 60)
    if success:
        print("🎊 FRONTEND TEST SETUP COMPLETE!")
        print("   ✅ Profile created and configured")
        print("   ✅ Facebook cookies simulated")
        print("   ✅ API tested and working")
        print("   ✅ Ready for frontend testing")
        
        print(f"\n🌐 Frontend URL: http://localhost:3001")
        print(f"🔧 Backend URL: http://localhost:8000")
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. Open http://localhost:3001 in browser")
        print(f"   2. Test the Check button functionality")
        print(f"   3. Verify browser opens and navigates to Facebook")
        
    else:
        print("💥 FRONTEND TEST SETUP FAILED!")
        print("   ❌ Cannot proceed with frontend testing")
        print("   🔧 Check backend and API issues")
    
    print(f"\nSetup completed.")
