#!/bin/bash

# Facebook Automation App - Production Build Script
# This script builds all components for production deployment

set -e

echo "🏗️ Building Facebook Automation App for Production"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Build configuration
BUILD_DIR="dist"
PLATFORMS=("linux" "windows" "darwin")
ARCHITECTURES=("amd64")

# Check prerequisites
check_prerequisites() {
    print_step "Checking build prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16.x or higher."
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check Go
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.19 or higher."
        exit 1
    fi
    
    print_status "All build prerequisites are available ✓"
}

# Clean previous builds
clean_builds() {
    print_step "Cleaning previous builds..."
    
    # Remove existing build directory
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    # Create build directory structure
    mkdir -p "$BUILD_DIR"/{go-services,backend,frontend,desktop}
    
    print_status "Build directory cleaned ✓"
}

# Build Go microservices
build_go_services() {
    print_step "Building Go microservices for production..."
    
    # Build ChromedpExtractor for multiple platforms
    print_status "Building ChromedpExtractor service..."
    cd backend/go-services/chromedp-extractor
    
    for platform in "${PLATFORMS[@]}"; do
        for arch in "${ARCHITECTURES[@]}"; do
            output_name="chromedp-extractor"
            if [ "$platform" = "windows" ]; then
                output_name="chromedp-extractor.exe"
            fi
            
            output_dir="../../../$BUILD_DIR/go-services/$platform-$arch"
            mkdir -p "$output_dir"
            
            print_status "Building ChromedpExtractor for $platform/$arch..."
            GOOS=$platform GOARCH=$arch go build -ldflags="-s -w" -o "$output_dir/$output_name" main.go
        done
    done
    
    cd ../../..
    
    # Build CollyParser for multiple platforms
    print_status "Building CollyParser service..."
    cd backend/go-services/colly-parser
    
    for platform in "${PLATFORMS[@]}"; do
        for arch in "${ARCHITECTURES[@]}"; do
            output_name="colly-parser"
            if [ "$platform" = "windows" ]; then
                output_name="colly-parser.exe"
            fi
            
            output_dir="../../../$BUILD_DIR/go-services/$platform-$arch"
            mkdir -p "$output_dir"
            
            print_status "Building CollyParser for $platform/$arch..."
            GOOS=$platform GOARCH=$arch go build -ldflags="-s -w" -o "$output_dir/$output_name" main.go
        done
    done
    
    cd ../../..
    
    print_status "Go microservices built successfully ✓"
}

# Build Python backend
build_backend() {
    print_step "Building Python backend..."
    
    cd backend
    
    # Create virtual environment for build
    if [ ! -d "build_venv" ]; then
        print_status "Creating build virtual environment..."
        python3 -m venv build_venv
    fi
    
    # Activate virtual environment
    source build_venv/bin/activate
    
    # Install dependencies
    print_status "Installing production dependencies..."
    pip install -r requirements.txt
    pip install pyinstaller
    
    # Create production requirements
    print_status "Generating production requirements..."
    pip freeze > ../dist/backend/requirements.txt
    
    # Copy backend files
    print_status "Copying backend files..."
    cp -r app ../dist/backend/
    cp -r automation ../dist/backend/
    cp -r alembic ../dist/backend/
    cp main.py ../dist/backend/
    cp alembic.ini ../dist/backend/
    
    # Copy environment template
    if [ -f ".env.example" ]; then
        cp .env.example ../dist/backend/
    fi
    
    # Create startup script
    cat > ../dist/backend/start.sh << 'EOF'
#!/bin/bash
# Production startup script for Python backend

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Set production environment
export ENVIRONMENT=production
export HYBRID_MODE=enabled
export GO_SERVICES_ENABLED=true

# Start with Gunicorn
exec gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
EOF
    
    chmod +x ../dist/backend/start.sh
    
    # Create Windows startup script
    cat > ../dist/backend/start.bat << 'EOF'
@echo off
REM Production startup script for Python backend (Windows)

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

REM Set production environment
set ENVIRONMENT=production
set HYBRID_MODE=enabled
set GO_SERVICES_ENABLED=true

REM Start with Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
EOF
    
    deactivate
    cd ..
    
    print_status "Python backend built successfully ✓"
}

# Build frontend
build_frontend() {
    print_step "Building React frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm ci --production=false
    
    # Build for web
    print_status "Building web application..."
    npm run build
    
    # Copy web build
    cp -r build ../dist/frontend/web
    
    # Build desktop applications
    print_status "Building desktop applications..."
    
    # Build for current platform
    npm run electron-build
    
    # Copy desktop builds
    if [ -d "dist" ]; then
        cp -r dist ../dist/desktop/
    fi
    
    cd ..
    
    print_status "Frontend built successfully ✓"
}

# Create deployment packages
create_packages() {
    print_step "Creating deployment packages..."
    
    cd "$BUILD_DIR"
    
    # Create platform-specific packages
    for platform in "${PLATFORMS[@]}"; do
        for arch in "${ARCHITECTURES[@]}"; do
            package_name="facebook-automation-$platform-$arch"
            package_dir="packages/$package_name"
            
            print_status "Creating package for $platform/$arch..."
            mkdir -p "$package_dir"
            
            # Copy Go services
            if [ -d "go-services/$platform-$arch" ]; then
                cp -r "go-services/$platform-$arch" "$package_dir/go-services"
            fi
            
            # Copy backend
            cp -r backend "$package_dir/"
            
            # Copy frontend web build
            cp -r frontend "$package_dir/"
            
            # Create platform-specific startup script
            if [ "$platform" = "windows" ]; then
                cat > "$package_dir/start.bat" << 'EOF'
@echo off
echo Starting Facebook Automation App...

REM Start Go services
start /B go-services\chromedp-extractor.exe
start /B go-services\colly-parser.exe

REM Wait for services to start
timeout /t 3 /nobreak > nul

REM Start Python backend
cd backend
start /B call start.bat
cd ..

echo All services started!
echo Open http://localhost:8000 in your browser
pause
EOF
            else
                cat > "$package_dir/start.sh" << 'EOF'
#!/bin/bash
echo "Starting Facebook Automation App..."

# Start Go services
./go-services/chromedp-extractor &
./go-services/colly-parser &

# Wait for services to start
sleep 3

# Start Python backend
cd backend
./start.sh &
cd ..

echo "All services started!"
echo "Open http://localhost:8000 in your browser"
EOF
                chmod +x "$package_dir/start.sh"
            fi
            
            # Create archive
            if [ "$platform" = "windows" ]; then
                zip -r "$package_name.zip" "$package_dir"
            else
                tar -czf "$package_name.tar.gz" "$package_dir"
            fi
            
            print_status "Package created: $package_name ✓"
        done
    done
    
    cd ..
    
    print_status "Deployment packages created successfully ✓"
}

# Create Docker images
build_docker() {
    print_step "Building Docker images..."
    
    # Build backend image
    print_status "Building backend Docker image..."
    docker build -t facebook-automation-backend:latest -f Dockerfile.backend .
    
    # Build frontend image
    print_status "Building frontend Docker image..."
    docker build -t facebook-automation-frontend:latest -f Dockerfile.frontend .
    
    # Build Go services images
    print_status "Building ChromedpExtractor Docker image..."
    docker build -t chromedp-extractor:latest backend/go-services/chromedp-extractor/
    
    print_status "Building CollyParser Docker image..."
    docker build -t colly-parser:latest backend/go-services/colly-parser/
    
    print_status "Docker images built successfully ✓"
}

# Generate build report
generate_report() {
    print_step "Generating build report..."
    
    report_file="$BUILD_DIR/build-report.txt"
    
    cat > "$report_file" << EOF
Facebook Automation App - Production Build Report
================================================

Build Date: $(date)
Build Platform: $(uname -s) $(uname -m)

Components Built:
- Go Microservices (ChromedpExtractor, CollyParser)
- Python Backend with Hybrid System
- React Frontend (Web + Desktop)

Platforms Supported:
$(for platform in "${PLATFORMS[@]}"; do echo "- $platform"; done)

Architectures:
$(for arch in "${ARCHITECTURES[@]}"; do echo "- $arch"; done)

Build Artifacts:
$(find "$BUILD_DIR" -type f -name "*.tar.gz" -o -name "*.zip" | sed 's/^/- /')

Docker Images:
- facebook-automation-backend:latest
- facebook-automation-frontend:latest
- chromedp-extractor:latest
- colly-parser:latest

Deployment Instructions:
1. Extract the appropriate platform package
2. Run the startup script (start.sh or start.bat)
3. Access the application at http://localhost:8000

For Docker deployment:
docker-compose up

Build completed successfully!
EOF
    
    print_status "Build report generated: $report_file ✓"
}

# Display build summary
show_build_summary() {
    echo ""
    echo "🎉 Production Build Completed Successfully!"
    echo "=========================================="
    echo ""
    echo "📦 Build Artifacts:"
    echo "  • Location: $BUILD_DIR/"
    echo "  • Packages: $BUILD_DIR/packages/"
    echo "  • Report: $BUILD_DIR/build-report.txt"
    echo ""
    echo "🐳 Docker Images:"
    echo "  • facebook-automation-backend:latest"
    echo "  • facebook-automation-frontend:latest"
    echo "  • chromedp-extractor:latest"
    echo "  • colly-parser:latest"
    echo ""
    echo "🚀 Deployment Options:"
    echo "  • Extract platform package and run start script"
    echo "  • Use Docker Compose: docker-compose up"
    echo "  • Deploy individual components as needed"
    echo ""
    echo "📋 Next Steps:"
    echo "  • Test deployment in staging environment"
    echo "  • Configure production environment variables"
    echo "  • Set up monitoring and logging"
    echo ""
}

# Main execution
main() {
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    # Parse command line arguments
    SKIP_DOCKER=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-docker)
                SKIP_DOCKER=true
                shift
                ;;
            --help|-h)
                echo "Facebook Automation App - Production Build Script"
                echo ""
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --skip-docker    Skip Docker image building"
                echo "  --help, -h       Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    check_prerequisites
    clean_builds
    build_go_services
    build_backend
    build_frontend
    create_packages
    
    if [ "$SKIP_DOCKER" = false ]; then
        if command -v docker &> /dev/null; then
            build_docker
        else
            print_warning "Docker not found, skipping Docker image builds"
        fi
    fi
    
    generate_report
    show_build_summary
}

# Run main function
main "$@"
