#!/bin/bash

# Facebook Automation App - Development Startup Script
# This script starts all services required for development

set -e

echo "🚀 Starting Facebook Automation App - Development Mode"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo "${BLUE}[STEP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16.x or higher."
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check Go
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.19 or higher."
        exit 1
    fi
    
    print_status "All prerequisites are installed ✓"
}

# Build Go services - REMOVED (Using Zendriver only)
build_go_services() {
    print_step "Skipping Go microservices (using Zendriver only)..."
    print_status "Go services build skipped ✓"
}

# Setup Python backend
setup_backend() {
    print_step "Setting up Python backend..."

    cd backend

    # Clear Python cache
    print_status "Clearing Python cache..."
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true

    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi

    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate

    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip

    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt

    # Check database and run migrations if needed
    print_status "Checking database..."
    if [ ! -f "facebook_automation.db" ]; then
        print_status "Database not found, will be created on first run..."
    fi

    # Check if .env exists
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_status "Creating .env from .env.example..."
            cp .env.example .env
        else
            print_warning ".env file not found. Please create one manually."
        fi
    fi

    cd ..
    print_status "Backend setup completed ✓"
}

# Setup frontend
setup_frontend() {
    print_step "Setting up React frontend..."

    cd frontend

    # Clear npm cache if needed
    if [ -d "node_modules" ]; then
        print_status "Checking npm dependencies..."
        npm ci --silent || npm install
    else
        print_status "Installing Node.js dependencies..."
        npm install
    fi

    cd ..
    print_status "Frontend setup completed ✓"
}

# Start services
start_services() {
    print_step "Starting all services..."

    # Create logs directory
    mkdir -p logs

    # Kill any existing processes on the ports
    print_status "Checking for existing processes..."
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true

    # Start Python backend
    print_status "Starting Python backend on port 8000..."
    cd backend

    # Ensure virtual environment is activated
    if [ ! -f "venv/bin/activate" ]; then
        print_error "Virtual environment not found. Please run setup first."
        exit 1
    fi

    # Start backend with proper error handling
    source venv/bin/activate
    nohup python main.py > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    cd ..

    # Wait for backend to start and check if it's running
    print_status "Waiting for backend to start..."
    sleep 5

    # Check if backend is actually running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend failed to start. Check logs/backend.log for details."
        cat logs/backend.log | tail -20
        exit 1
    fi

    # Test backend health
    for i in {1..10}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            print_status "Backend is healthy ✓"
            break
        fi
        if [ $i -eq 10 ]; then
            print_error "Backend health check failed after 10 attempts"
            cat logs/backend.log | tail -20
            exit 1
        fi
        sleep 2
    done

    # Start frontend
    print_status "Starting React frontend on port 3000..."
    cd frontend
    nohup npm start > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    cd ..

    print_status "All services started successfully ✓"
}

# Check service health
check_services() {
    print_step "Final service health check..."

    # Check Python backend API endpoints
    print_status "Testing backend API endpoints..."

    # Test basic health endpoint
    if curl -s http://localhost:8000/health > /dev/null; then
        print_status "Backend health endpoint ✓"
    else
        print_warning "Backend health endpoint not responding"
    fi

    # Test profiles endpoint
    if curl -s http://localhost:8000/api/profiles > /dev/null; then
        print_status "Profiles API endpoint ✓"
    else
        print_warning "Profiles API endpoint not responding"
    fi

    # Test scraping sessions endpoint
    if curl -s http://localhost:8000/api/scraping/sessions > /dev/null; then
        print_status "Scraping sessions API endpoint ✓"
    else
        print_warning "Scraping sessions API endpoint not responding"
    fi

    # Check frontend (it takes longer to start)
    print_status "Frontend is starting... (this may take a moment)"
    print_status "You can check frontend status at: http://localhost:3000"
}

# Display service information
show_service_info() {
    echo ""
    echo "🎉 Facebook Automation App is now running!"
    echo "=========================================="
    echo ""
    echo "📊 Service Status:"
    echo "  • Python Backend:    http://localhost:8000 (API + Zendriver)"
    echo "  • React Frontend:    http://localhost:3000"
    echo ""
    echo "🔧 Key API Endpoints:"
    echo "  • Health Check:      http://localhost:8000/health"
    echo "  • Profiles:          http://localhost:8000/api/profiles"
    echo "  • Scraping Sessions: http://localhost:8000/api/scraping/sessions"
    echo ""
    echo "📁 Logs are available in the 'logs/' directory"
    echo "📋 Process IDs are stored in 'logs/*.pid' files"
    echo ""
    echo "🛑 To stop all services, run: ./scripts/stop-dev.sh"
    echo "🧪 To run tests, run: ./scripts/test-suite.sh"
    echo ""
    echo "🌐 Open your browser and navigate to: http://localhost:3000"
    echo ""
    echo "💡 If scraping fails, check:"
    echo "  • Backend logs: tail -f logs/backend.log"
    echo "  • Browser profiles are properly configured"
    echo "  • Facebook cookies are saved in profiles"
    echo ""
}

# Cleanup function
cleanup() {
    print_warning "Received interrupt signal. Cleaning up..."
    
    # Kill services if PIDs exist
    if [ -f "logs/frontend.pid" ]; then
        kill $(cat logs/frontend.pid) 2>/dev/null || true
        rm logs/frontend.pid
    fi
    
    if [ -f "logs/backend.pid" ]; then
        kill $(cat logs/backend.pid) 2>/dev/null || true
        rm logs/backend.pid
    fi
    
    if [ -f "logs/colly-parser.pid" ]; then
        kill $(cat logs/colly-parser.pid) 2>/dev/null || true
        rm logs/colly-parser.pid
    fi
    
    if [ -f "logs/chromedp-extractor.pid" ]; then
        kill $(cat logs/chromedp-extractor.pid) 2>/dev/null || true
        rm logs/chromedp-extractor.pid
    fi
    
    print_status "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    check_prerequisites
    setup_backend
    # setup_frontend
    start_services
    check_services
    show_service_info
    
    # Keep script running
    print_status "Press Ctrl+C to stop all services"
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
