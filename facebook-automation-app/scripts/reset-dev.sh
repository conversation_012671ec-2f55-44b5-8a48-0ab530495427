#!/bin/bash

# Facebook Automation App - Development Reset Script
# This script clears cache and resets the development environment

set -e

echo "🧹 Resetting Facebook Automation App - Development Environment"
echo "=============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo "${BLUE}[STEP]${NC} $1"
}

# Stop all services
stop_services() {
    print_step "Stopping all services..."
    
    # Kill processes by port
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    
    # Kill processes by PID files
    if [ -f "logs/backend.pid" ]; then
        kill $(cat logs/backend.pid) 2>/dev/null || true
        rm logs/backend.pid
    fi
    
    if [ -f "logs/frontend.pid" ]; then
        kill $(cat logs/frontend.pid) 2>/dev/null || true
        rm logs/frontend.pid
    fi
    
    print_status "Services stopped ✓"
}

# Clear Python cache
clear_python_cache() {
    print_step "Clearing Python cache..."
    
    cd backend
    
    # Remove __pycache__ directories
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    
    # Remove .pyc files
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # Remove .pyo files
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    # Clear pip cache
    pip cache purge 2>/dev/null || true
    
    cd ..
    print_status "Python cache cleared ✓"
}

# Clear Node.js cache
clear_node_cache() {
    print_step "Clearing Node.js cache..."
    
    cd frontend
    
    # Clear npm cache
    npm cache clean --force 2>/dev/null || true
    
    # Remove node_modules (optional - uncomment if needed)
    # rm -rf node_modules
    
    cd ..
    print_status "Node.js cache cleared ✓"
}

# Clear logs
clear_logs() {
    print_step "Clearing logs..."
    
    if [ -d "logs" ]; then
        rm -rf logs/*.log 2>/dev/null || true
        rm -rf logs/*.pid 2>/dev/null || true
    fi
    
    print_status "Logs cleared ✓"
}

# Reset database (optional)
reset_database() {
    print_step "Checking database reset option..."
    
    if [ "$1" = "--reset-db" ]; then
        print_warning "Resetting database..."
        
        cd backend
        
        # Backup existing database
        if [ -f "facebook_automation.db" ]; then
            cp facebook_automation.db "facebook_automation.db.backup.$(date +%Y%m%d_%H%M%S)"
            print_status "Database backed up"
        fi
        
        # Remove database
        rm -f facebook_automation.db
        rm -f facebook_automation.db-shm
        rm -f facebook_automation.db-wal
        
        cd ..
        print_status "Database reset ✓"
    else
        print_status "Database preserved (use --reset-db to reset)"
    fi
}

# Clear browser profiles cache (optional)
clear_browser_cache() {
    print_step "Checking browser cache reset option..."
    
    if [ "$1" = "--reset-browser" ] || [ "$2" = "--reset-browser" ]; then
        print_warning "Clearing browser profiles cache..."
        
        cd backend
        
        if [ -d "browser_profiles" ]; then
            # Clear cache but keep profile structure
            find browser_profiles -name "Cache" -type d -exec rm -rf {} + 2>/dev/null || true
            find browser_profiles -name "Code Cache" -type d -exec rm -rf {} + 2>/dev/null || true
            find browser_profiles -name "GPUCache" -type d -exec rm -rf {} + 2>/dev/null || true
            find browser_profiles -name "Service Worker" -type d -exec rm -rf {} + 2>/dev/null || true
        fi
        
        cd ..
        print_status "Browser cache cleared ✓"
    else
        print_status "Browser profiles preserved (use --reset-browser to clear cache)"
    fi
}

# Main execution
main() {
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    stop_services
    clear_python_cache
    clear_node_cache
    clear_logs
    reset_database "$1"
    clear_browser_cache "$1" "$2"
    
    echo ""
    echo "🎉 Development environment reset completed!"
    echo "==========================================="
    echo ""
    echo "💡 Usage options:"
    echo "  • ./scripts/reset-dev.sh                 - Basic reset (preserve DB & browser)"
    echo "  • ./scripts/reset-dev.sh --reset-db      - Reset including database"
    echo "  • ./scripts/reset-dev.sh --reset-browser - Reset including browser cache"
    echo "  • ./scripts/reset-dev.sh --reset-db --reset-browser - Full reset"
    echo ""
    echo "🚀 To start services again, run: ./scripts/start-dev.sh"
    echo ""
}

# Run main function
main "$@"
