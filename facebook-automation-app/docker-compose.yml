version: '3.8'

services:
  # ChromedpExtractor Go Service
  chromedp-extractor:
    build:
      context: ./backend/go-services/chromedp-extractor
      dockerfile: Dockerfile
    container_name: chromedp-extractor
    ports:
      - "8081:8081"
    environment:
      - PORT=8081
      - HOST=0.0.0.0
      - MAX_CONNECTIONS=20
      - TIMEOUT=30s
      - DEBUG=false
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # CollyParser Go Service
  colly-parser:
    build:
      context: ./backend/go-services/colly-parser
      dockerfile: Dockerfile
    container_name: colly-parser
    ports:
      - "8082:8082"
    environment:
      - PORT=8082
      - HOST=0.0.0.0
      - PARALLEL_WORKERS=8
      - TIMEOUT=30s
      - DEBUG=false
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: facebook-automation-db
    environment:
      - POSTGRES_DB=facebook_automation
      - POSTGRES_USER=facebook_user
      - POSTGRES_PASSWORD=facebook_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U facebook_user -d facebook_automation"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: facebook-automation-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Python Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: facebook-automation-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=**********************************************************/facebook_automation
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=false
      - HYBRID_MODE=enabled
      - GO_SERVICES_ENABLED=true
      - CHROMEDP_SERVICE_HOST=chromedp-extractor
      - CHROMEDP_SERVICE_PORT=8081
      - COLLY_SERVICE_HOST=colly-parser
      - COLLY_SERVICE_PORT=8082
      - MAX_CONCURRENT_SESSIONS=5
      - SHARED_MEMORY_SIZE_MB=1000
      - CONNECTION_POOL_SIZE=10
      - BROWSER_TIMEOUT=60
      - SERVICE_DISCOVERY_ENABLED=true
      - ERROR_RECOVERY_ENABLED=true
      - PERFORMANCE_MONITORING=true
    volumes:
      - ./backend/profiles:/app/profiles
      - ./backend/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      chromedp-extractor:
        condition: service_healthy
      colly-parser:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Frontend (Production Build)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: facebook-automation-frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_HYBRID_MODE=enabled
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: facebook-automation-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: facebook-automation-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - facebook-automation-network

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: facebook-automation-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - facebook-automation-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  facebook-automation-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
